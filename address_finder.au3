; Simple Address Finder for Tantra
; Run this while your character is in-game with visible HP

#include <GUIConstantsEx.au3>

; Memory functions from your bot
Func _MemRead($i_hProcess, $i_lpBaseAddress, $i_nSize, $v_lpNumberOfBytesRead = '')
    Local $v_Struct = DllStructCreate ('byte[' & $i_nSize & ']')
    DllCall('kernel32.dll', 'int', 'ReadProcessMemory', 'int', $i_hProcess, 'int', $i_lpBaseAddress, 'int', DllStructGetPtr ($v_Struct, 1), 'int', $i_nSize, 'int', $v_lpNumberOfBytesRead)
    Local $v_Return = DllStructGetData ($v_Struct, 1)
    $v_Struct=0
    Return $v_Return
EndFunc

Func _MemOpen($i_dwDesiredAccess, $i_bInheritHandle, $i_dwProcessId)
    $ai_Handle = DllCall("kernel32.dll", 'int', 'OpenProcess', 'int', $i_dwDesiredAccess, 'int', $i_bInheritHandle, 'int', $i_dwProcessId)
    If @error Then
        SetError(1)
        Return 0
    EndIf
    Return $ai_Handle[0]
EndFunc

Func _MemClose($i_hProcess)
    $av_CloseHandle = DllCall('kernel32.dll', 'int', 'CloseHandle', 'int', $i_hProcess)
    Return $av_CloseHandle[0]
EndFunc

; Test specific addresses
Func TestAddress($baseAddr, $description)
    Local $process = "HTLauncher.exe"
    Local $pid = ProcessExists($process)
    
    If $pid = 0 Then
        ConsoleWrite("HTLauncher.exe not found!" & @CRLF)
        Return False
    EndIf
    
    Local $handle = _MemOpen(0x1F0FFF, False, $pid)
    If $handle = 0 Then
        ConsoleWrite("Could not open process memory." & @CRLF)
        Return False
    EndIf
    
    ; Read HP values
    Local $curHP = ReadDWORD($handle, $baseAddr + 272)  ; Current HP offset
    Local $maxHP = ReadDWORD($handle, $baseAddr + 268)  ; Max HP offset
    
    ConsoleWrite($description & " (0x" & Hex($baseAddr) & "): ")
    
    If $curHP > 0 And $maxHP > 0 And $curHP <= $maxHP And $maxHP < 100000 Then
        ConsoleWrite("VALID - HP: " & $curHP & "/" & $maxHP & @CRLF)
        _MemClose($handle)
        Return True
    Else
        ConsoleWrite("Invalid - HP: " & $curHP & "/" & $maxHP & @CRLF)
    EndIf
    
    _MemClose($handle)
    Return False
EndFunc

Func ReadDWORD($handle, $address)
    Local $data = _MemRead($handle, $address, 4)
    If @error Then Return 0
    
    ; Convert binary to DWORD
    Local $struct = DllStructCreate("dword", DllStructGetPtr(DllStructCreate("byte[4]")))
    DllStructSetData($struct, 1, $data)
    Return DllStructGetData($struct, 1)
EndFunc

; Main execution
ConsoleWrite("=== Tantra Address Finder ===" & @CRLF)
ConsoleWrite("Make sure your character is in-game with visible HP!" & @CRLF & @CRLF)

; Test old addresses first
ConsoleWrite("Testing old addresses:" & @CRLF)
TestAddress(0x109BE1D0, "Old Character Base")
TestAddress(0x109BE280, "Old Monster Target")

ConsoleWrite(@CRLF & "Testing nearby addresses:" & @CRLF)

; Test addresses in increments around the old ones
Local $oldCharBase = 0x109BE1D0
Local $oldMonsterBase = 0x109BE280

; Test character base addresses
For $offset = -0x100000 To 0x100000 Step 0x10000
    Local $testAddr = $oldCharBase + $offset
    If TestAddress($testAddr, "Character Base Test") Then
        ConsoleWrite("*** FOUND POTENTIAL CHARACTER BASE: 0x" & Hex($testAddr) & " ***" & @CRLF)
    EndIf
Next

ConsoleWrite(@CRLF & "Testing monster target addresses:" & @CRLF)

; Test monster target addresses  
For $offset = -0x100000 To 0x100000 Step 0x10000
    Local $testAddr = $oldMonsterBase + $offset
    If TestMonsterAddress($testAddr) Then
        ConsoleWrite("*** FOUND POTENTIAL MONSTER TARGET: 0x" & Hex($testAddr) & " ***" & @CRLF)
    EndIf
Next

Func TestMonsterAddress($baseAddr)
    Local $process = "HTLauncher.exe"
    Local $pid = ProcessExists($process)
    
    If $pid = 0 Then Return False
    
    Local $handle = _MemOpen(0x1F0FFF, False, $pid)
    If $handle = 0 Then Return False
    
    ; Try to read monster name (first 20 bytes)
    Local $nameData = ""
    For $i = 0 To 19
        Local $byte = _MemRead($handle, $baseAddr + $i, 1)
        If @error Then
            _MemClose($handle)
            Return False
        EndIf
        
        Local $char = BinaryToString($byte)
        If Asc($char) >= 32 And Asc($char) <= 126 Then ; Printable ASCII
            $nameData &= $char
        ElseIf Asc($char) = 0 Then ; Null terminator
            ExitLoop
        Else
            $nameData &= "?"
        EndIf
    Next
    
    _MemClose($handle)
    
    If StringLen($nameData) >= 3 And StringLen($nameData) <= 15 Then
        ConsoleWrite("Monster Target Test (0x" & Hex($baseAddr) & "): VALID - Name: " & $nameData & @CRLF)
        Return True
    EndIf
    
    Return False
EndFunc

ConsoleWrite(@CRLF & "Scan complete! Check the output above for FOUND POTENTIAL addresses." & @CRLF)
ConsoleWrite("Press any key to exit..." & @CRLF)
ConsoleRead()
