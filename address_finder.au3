; AUTOMATIC Address Finder for Tantra - NO CODING REQUIRED!
; Just run this while your character is in-game and it will find the new addresses

#include <GUIConstantsEx.au3>
#include <WindowsConstants.au3>

; Memory functions from your bot
Func _MemRead($i_hProcess, $i_lpBaseAddress, $i_nSize, $v_lpNumberOfBytesRead = '')
    Local $v_Struct = DllStructCreate ('byte[' & $i_nSize & ']')
    DllCall('kernel32.dll', 'int', 'ReadProcessMemory', 'int', $i_hProcess, 'int', $i_lpBaseAddress, 'int', DllStructGetPtr ($v_Struct, 1), 'int', $i_nSize, 'int', $v_lpNumberOfBytesRead)
    Local $v_Return = DllStructGetData ($v_Struct, 1)
    $v_Struct=0
    Return $v_Return
EndFunc

Func _MemOpen($i_dwDesiredAccess, $i_bInheritHandle, $i_dwProcessId)
    $ai_Handle = DllCall("kernel32.dll", 'int', 'OpenProcess', 'int', $i_dwDesiredAccess, 'int', $i_bInheritHandle, 'int', $i_dwProcessId)
    If @error Then
        SetError(1)
        Return 0
    EndIf
    Return $ai_Handle[0]
EndFunc

Func _MemClose($i_hProcess)
    $av_CloseHandle = DllCall('kernel32.dll', 'int', 'CloseHandle', 'int', $i_hProcess)
    Return $av_CloseHandle[0]
EndFunc

; Create a simple GUI to show results
Global $hGUI = GUICreate("Tantra Address Finder - AUTOMATIC", 700, 500)
Global $lblInstructions = GUICtrlCreateLabel("INSTRUCTIONS:" & @CRLF & "1. Make sure your character is logged into Tantra" & @CRLF & "2. Click 'Find New Addresses' button" & @CRLF & "3. Wait for the scan to complete" & @CRLF & "4. Copy the new addresses to update your bot", 10, 10, 680, 80)
Global $btnScan = GUICtrlCreateButton("Find New Addresses", 10, 100, 200, 40)
Global $lblStatus = GUICtrlCreateLabel("Status: Ready to scan", 10, 150, 680, 20)
Global $editResults = GUICtrlCreateEdit("", 10, 180, 680, 280, BitOR($ES_MULTILINE, $ES_AUTOVSCROLL, $ES_READONLY))
Global $btnCopy = GUICtrlCreateButton("Copy Results to Clipboard", 220, 100, 200, 40)
Global $btnUpdateBot = GUICtrlCreateButton("Auto-Update Bot File", 430, 100, 200, 40)

GUICtrlSetState($btnCopy, $GUI_DISABLE)
GUICtrlSetState($btnUpdateBot, $GUI_DISABLE)

GUISetState(@SW_SHOW)

Global $foundCharBase = ""
Global $foundMonsterBase = ""

; Main GUI loop
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE
            Exit
        Case $btnScan
            FindNewAddresses()
        Case $btnCopy
            CopyResultsToClipboard()
        Case $btnUpdateBot
            UpdateBotFile()
    EndSwitch
WEnd

; Main function to find new addresses automatically
Func FindNewAddresses()
    GUICtrlSetData($lblStatus, "Status: Starting automatic scan...")
    GUICtrlSetData($editResults, "=== AUTOMATIC TANTRA ADDRESS FINDER ===" & @CRLF & @CRLF)

    ; Check if game is running
    Local $process = "HTLauncher.exe"
    Local $pid = ProcessExists($process)

    If $pid = 0 Then
        GUICtrlSetData($editResults, "ERROR: HTLauncher.exe not found!" & @CRLF & "Please make sure Tantra is running and try again." & @CRLF, 1)
        GUICtrlSetData($lblStatus, "Status: Error - Game not running")
        Return
    EndIf

    GUICtrlSetData($editResults, "✓ Found Tantra process (PID: " & $pid & ")" & @CRLF, 1)

    ; Open process memory
    Local $handle = _MemOpen(0x1F0FFF, False, $pid)
    If $handle = 0 Then
        GUICtrlSetData($editResults, "ERROR: Could not access game memory!" & @CRLF & "Try running as Administrator." & @CRLF, 1)
        GUICtrlSetData($lblStatus, "Status: Error - Memory access denied")
        Return
    EndIf

    GUICtrlSetData($editResults, "✓ Successfully opened game memory" & @CRLF & @CRLF, 1)
    GUICtrlSetData($lblStatus, "Status: Scanning for character base address...")

    ; Find Character Base Address
    FindCharacterBase($handle)

    GUICtrlSetData($lblStatus, "Status: Scanning for monster target address...")

    ; Find Monster Target Address
    FindMonsterTarget($handle)

    _MemClose($handle)

    If $foundCharBase <> "" And $foundMonsterBase <> "" Then
        GUICtrlSetData($editResults, @CRLF & "🎉 SUCCESS! Found both addresses!" & @CRLF, 1)
        GUICtrlSetData($editResults, "You can now copy these addresses or auto-update your bot." & @CRLF, 1)
        GUICtrlSetState($btnCopy, $GUI_ENABLE)
        GUICtrlSetState($btnUpdateBot, $GUI_ENABLE)
        GUICtrlSetData($lblStatus, "Status: Scan complete - Addresses found!")
    Else
        GUICtrlSetData($editResults, @CRLF & "⚠️ Could not find all addresses. Try again or contact support." & @CRLF, 1)
        GUICtrlSetData($lblStatus, "Status: Scan incomplete - Some addresses not found")
    EndIf
EndFunc

; Find Character Base Address
Func FindCharacterBase($handle)
    GUICtrlSetData($editResults, "Searching for Character Base Address..." & @CRLF, 1)

    ; Test common address ranges where character data might be
    Local $testRanges[20] = [0x109BE1D0, 0x10A0E1D0, 0x10ABE1D0, 0x109CE1D0, 0x109DE1D0, 0x109EE1D0, 0x109FE1D0, 0x10A1E1D0, 0x10A2E1D0, 0x10A3E1D0, 0x10A4E1D0, 0x10A5E1D0, 0x10A6E1D0, 0x10A7E1D0, 0x10A8E1D0, 0x10A9E1D0, 0x10AAE1D0, 0x10ACE1D0, 0x10ADE1D0, 0x10AEE1D0]

    For $i = 0 To UBound($testRanges) - 1
        Local $testAddr = $testRanges[$i]
        Local $curHP = ReadDWORD($handle, $testAddr + 272)  ; Current HP offset
        Local $maxHP = ReadDWORD($handle, $testAddr + 268)  ; Max HP offset
        Local $curTP = ReadDWORD($handle, $testAddr + 808)  ; Current TP offset
        Local $maxTP = ReadDWORD($handle, $testAddr + 804)  ; Max TP offset

        ; Check if this looks like valid character data
        If $curHP > 0 And $maxHP > 0 And $curHP <= $maxHP And $maxHP < 100000 And $maxHP > 100 Then
            If $curTP >= 0 And $maxTP > 0 And $curTP <= $maxTP And $maxTP < 100000 Then
                GUICtrlSetData($editResults, "✓ FOUND CHARACTER BASE: 0x" & Hex($testAddr) & @CRLF, 1)
                GUICtrlSetData($editResults, "  Current HP: " & $curHP & " / Max HP: " & $maxHP & @CRLF, 1)
                GUICtrlSetData($editResults, "  Current TP: " & $curTP & " / Max TP: " & $maxTP & @CRLF & @CRLF, 1)
                $foundCharBase = "0x" & Hex($testAddr)
                Return True
            EndIf
        EndIf
    Next

    GUICtrlSetData($editResults, "❌ Character base address not found in common locations" & @CRLF, 1)
    Return False
EndFunc

; Find Monster Target Address
Func FindMonsterTarget($handle)
    GUICtrlSetData($editResults, "Searching for Monster Target Address..." & @CRLF, 1)

    ; Test common address ranges where monster target data might be
    Local $testRanges[20] = [0x109BE280, 0x10A0E280, 0x10ABE280, 0x109CE280, 0x109DE280, 0x109EE280, 0x109FE280, 0x10A1E280, 0x10A2E280, 0x10A3E280, 0x10A4E280, 0x10A5E280, 0x10A6E280, 0x10A7E280, 0x10A8E280, 0x10A9E280, 0x10AAE280, 0x10ACE280, 0x10ADE280, 0x10AEE280]

    For $i = 0 To UBound($testRanges) - 1
        Local $testAddr = $testRanges[$i]
        Local $monsterName = ReadString($handle, $testAddr, 20)

        ; Check if this looks like a valid monster name
        If StringLen($monsterName) >= 3 And StringLen($monsterName) <= 15 Then
            ; Check if it contains reasonable characters
            Local $validName = True
            For $j = 1 To StringLen($monsterName)
                Local $char = StringMid($monsterName, $j, 1)
                Local $ascii = Asc($char)
                If $ascii < 32 Or $ascii > 126 Then
                    $validName = False
                    ExitLoop
                EndIf
            Next

            If $validName Then
                GUICtrlSetData($editResults, "✓ FOUND MONSTER TARGET: 0x" & Hex($testAddr) & @CRLF, 1)
                GUICtrlSetData($editResults, "  Monster Name: " & $monsterName & @CRLF & @CRLF, 1)
                $foundMonsterBase = "0x" & Hex($testAddr)
                Return True
            EndIf
        EndIf
    Next

    GUICtrlSetData($editResults, "❌ Monster target address not found in common locations" & @CRLF, 1)
    GUICtrlSetData($editResults, "  (Try targeting a monster and run scan again)" & @CRLF, 1)
    Return False
EndFunc

; Copy results to clipboard
Func CopyResultsToClipboard()
    Local $clipText = "NEW TANTRA ADDRESSES FOUND:" & @CRLF & @CRLF

    If $foundCharBase <> "" Then
        $clipText &= "Character Base Address: " & $foundCharBase & @CRLF
    EndIf

    If $foundMonsterBase <> "" Then
        $clipText &= "Monster Target Address: " & $foundMonsterBase & @CRLF
    EndIf

    $clipText &= @CRLF & "Replace these in your bot script:" & @CRLF
    $clipText &= "$baseAddressCharacter = " & $foundCharBase & @CRLF
    $clipText &= "$baseAddressTargetMonster = " & $foundMonsterBase & @CRLF

    ClipPut($clipText)
    MsgBox(0, "Copied!", "New addresses copied to clipboard!" & @CRLF & @CRLF & "You can now paste them into your bot script.")
EndFunc

; Automatically update the bot file
Func UpdateBotFile()
    If $foundCharBase = "" Or $foundMonsterBase = "" Then
        MsgBox(0, "Error", "Cannot update bot - addresses not found yet!")
        Return
    EndIf

    ; Ask user to confirm
    Local $result = MsgBox(4, "Update Bot File", "This will automatically update your booster101.au3 file with the new addresses." & @CRLF & @CRLF & "Character Base: " & $foundCharBase & @CRLF & "Monster Target: " & $foundMonsterBase & @CRLF & @CRLF & "Continue?")

    If $result = 7 Then Return ; User clicked No

    ; Read the current bot file
    Local $botFile = FileOpen("booster101.au3", 0)
    If $botFile = -1 Then
        MsgBox(0, "Error", "Could not open booster101.au3 file!")
        Return
    EndIf

    Local $fileContent = FileRead($botFile)
    FileClose($botFile)

    ; Replace the old addresses
    $fileContent = StringReplace($fileContent, "$baseAddressCharacter = 0x109BE1D0", "$baseAddressCharacter = " & $foundCharBase)
    $fileContent = StringReplace($fileContent, "$baseAddressTargetMonster = 0x109BE280", "$baseAddressTargetMonster = " & $foundMonsterBase)

    ; Write the updated file
    Local $botFileWrite = FileOpen("booster101.au3", 2) ; Overwrite mode
    If $botFileWrite = -1 Then
        MsgBox(0, "Error", "Could not write to booster101.au3 file!")
        Return
    EndIf

    FileWrite($botFileWrite, $fileContent)
    FileClose($botFileWrite)

    MsgBox(0, "Success!", "Bot file updated successfully!" & @CRLF & @CRLF & "Your booster101.au3 now has the new addresses and should work with the updated game.")
EndFunc

; Helper functions
Func ReadDWORD($handle, $address)
    Local $data = _MemRead($handle, $address, 4)
    If @error Then Return 0

    Local $struct = DllStructCreate("dword")
    DllStructSetData($struct, 1, $data)
    Return DllStructGetData($struct, 1)
EndFunc

Func ReadString($handle, $address, $maxLen)
    Local $result = ""
    For $i = 0 To $maxLen - 1
        Local $byte = _MemRead($handle, $address + $i, 1)
        If @error Then Return $result

        Local $char = BinaryToString($byte)
        Local $ascii = Asc($char)

        If $ascii = 0 Then ; Null terminator
            Return $result
        ElseIf $ascii >= 32 And $ascii <= 126 Then ; Printable ASCII
            $result &= $char
        Else
            Return $result ; Stop at first non-printable character
        EndIf
    Next

    Return $result
EndFunc
