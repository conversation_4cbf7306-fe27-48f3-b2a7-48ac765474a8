; Quick Address Tester
; Edit the addresses below and run to test them

; Memory functions
Func _MemRead($i_hProcess, $i_lpBaseAddress, $i_nSize, $v_lpNumberOfBytesRead = '')
    Local $v_Struct = DllStructCreate ('byte[' & $i_nSize & ']')
    DllCall('kernel32.dll', 'int', 'ReadProcessMemory', 'int', $i_hProcess, 'int', $i_lpBaseAddress, 'int', DllStructGetPtr ($v_Struct, 1), 'int', $i_nSize, 'int', $v_lpNumberOfBytesRead)
    Local $v_Return = DllStructGetData ($v_Struct, 1)
    $v_Struct=0
    Return $v_Return
EndFunc

Func _MemOpen($i_dwDesiredAccess, $i_bInheritHandle, $i_dwProcessId)
    $ai_Handle = DllCall("kernel32.dll", 'int', 'OpenProcess', 'int', $i_dwDesiredAccess, 'int', $i_bInheritHandle, 'int', $i_dwProcessId)
    If @error Then
        SetError(1)
        Return 0
    EndIf
    Return $ai_Handle[0]
EndFunc

Func _MemClose($i_hProcess)
    $av_CloseHandle = DllCall('kernel32.dll', 'int', 'CloseHandle', 'int', $i_hProcess)
    Return $av_CloseHandle[0]
EndFunc

; Test these addresses - EDIT THESE VALUES
Local $testCharacterBase = 0x109BE1D0  ; <-- Edit this
Local $testMonsterTarget = 0x109BE280  ; <-- Edit this

; Get process
Local $process = "HTLauncher.exe"
Local $pid = ProcessExists($process)

If $pid = 0 Then
    MsgBox(0, "Error", "HTLauncher.exe not found! Make sure Tantra is running.")
    Exit
EndIf

Local $handle = _MemOpen(0x1F0FFF, False, $pid)
If $handle = 0 Then
    MsgBox(0, "Error", "Could not open process memory.")
    Exit
EndIf

; Test Character Base
Local $curHP = ReadDWORD($handle, $testCharacterBase + 272)
Local $maxHP = ReadDWORD($handle, $testCharacterBase + 268)
Local $curTP = ReadDWORD($handle, $testCharacterBase + 808)
Local $maxTP = ReadDWORD($handle, $testCharacterBase + 804)

Local $result = "Character Base Test (0x" & Hex($testCharacterBase) & "):" & @CRLF
$result &= "Current HP: " & $curHP & @CRLF
$result &= "Max HP: " & $maxHP & @CRLF
$result &= "Current TP: " & $curTP & @CRLF
$result &= "Max TP: " & $maxTP & @CRLF

If $curHP > 0 And $maxHP > 0 And $curHP <= $maxHP Then
    $result &= "STATUS: LOOKS VALID!" & @CRLF
Else
    $result &= "STATUS: Invalid values" & @CRLF
EndIf

; Test Monster Target
Local $monsterName = ""
For $i = 0 To 19
    Local $byte = _MemRead($handle, $testMonsterTarget + $i, 1)
    If @error Then ExitLoop
    
    Local $char = BinaryToString($byte)
    If Asc($char) >= 32 And Asc($char) <= 126 Then
        $monsterName &= $char
    ElseIf Asc($char) = 0 Then
        ExitLoop
    EndIf
Next

$result &= @CRLF & "Monster Target Test (0x" & Hex($testMonsterTarget) & "):" & @CRLF
$result &= "Monster Name: " & $monsterName & @CRLF

If StringLen($monsterName) >= 3 Then
    $result &= "STATUS: LOOKS VALID!" & @CRLF
Else
    $result &= "STATUS: No valid name found" & @CRLF
EndIf

_MemClose($handle)

; Show results
MsgBox(0, "Address Test Results", $result)

Func ReadDWORD($handle, $address)
    Local $data = _MemRead($handle, $address, 4)
    If @error Then Return 0
    
    Local $struct = DllStructCreate("dword")
    DllStructSetData($struct, 1, $data)
    Return DllStructGetData($struct, 1)
EndFunc
