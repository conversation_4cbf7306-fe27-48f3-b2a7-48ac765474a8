; Memory Address Scanner for Tantra Game
; This script helps find updated memory addresses after game updates

#include <GUIConstantsEx.au3>
#include <WindowsConstants.au3>

; Memory reading functions (copied from your bot)
Func _MemRead($i_hProcess, $i_lpBaseAddress, $i_nSize, $v_lpNumberOfBytesRead = '')
    Local $v_Struct = DllStructCreate ('byte[' & $i_nSize & ']')
    DllCall('kernel32.dll', 'int', 'ReadProcessMemory', 'int', $i_hProcess, 'int', $i_lpBaseAddress, 'int', DllStructGetPtr ($v_Struct, 1), 'int', $i_nSize, 'int', $v_lpNumberOfBytesRead)
    Local $v_Return = DllStructGetData ($v_Struct, 1)
    $v_Struct=0
    Return $v_Return
EndFunc

Func _MemOpen($i_dwDesiredAccess, $i_bInheritHandle, $i_dwProcessId)
    $ai_Handle = DllCall("kernel32.dll", 'int', 'OpenProcess', 'int', $i_dwDesiredAccess, 'int', $i_bInheritHandle, 'int', $i_dwProcessId)
    If @error Then
        SetError(1)
        Return 0
    EndIf
    Return $ai_Handle[0]
EndFunc

Func _MemClose($i_hProcess)
    $av_CloseHandle = DllCall('kernel32.dll', 'int', 'CloseHandle', 'int', $i_hProcess)
    Return $av_CloseHandle[0]
EndFunc

; Create GUI
$hGUI = GUICreate("Tantra Memory Address Scanner", 600, 400)
$btnScanChar = GUICtrlCreateButton("Scan for Character Base", 10, 10, 150, 30)
$btnScanMonster = GUICtrlCreateButton("Scan for Monster Target", 170, 10, 150, 30)
$btnScanRange = GUICtrlCreateButton("Scan Address Range", 330, 10, 150, 30)
$lblStatus = GUICtrlCreateLabel("Status: Ready", 10, 50, 580, 20)
$editResults = GUICtrlCreateEdit("", 10, 80, 580, 300, BitOR($ES_MULTILINE, $ES_AUTOVSCROLL, $ES_READONLY))

GUISetState(@SW_SHOW)

; Main loop
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE
            Exit
        Case $btnScanChar
            ScanForCharacterBase()
        Case $btnScanMonster
            ScanForMonsterTarget()
        Case $btnScanRange
            ScanAddressRange()
    EndSwitch
WEnd

Func ScanForCharacterBase()
    GUICtrlSetData($lblStatus, "Status: Scanning for Character Base Address...")
    GUICtrlSetData($editResults, "Scanning for Character Base Address..." & @CRLF, 1)
    
    ; Get Tantra process
    Local $process = "HTLauncher.exe"
    Local $pid = ProcessExists($process)
    
    If $pid = 0 Then
        GUICtrlSetData($editResults, "Error: HTLauncher.exe not found! Make sure Tantra is running." & @CRLF, 1)
        GUICtrlSetData($lblStatus, "Status: Error - Game not found")
        Return
    EndIf
    
    Local $handle = _MemOpen(0x1F0FFF, False, $pid)
    If $handle = 0 Then
        GUICtrlSetData($editResults, "Error: Could not open process memory." & @CRLF, 1)
        GUICtrlSetData($lblStatus, "Status: Error - Memory access denied")
        Return
    EndIf
    
    ; Scan common memory ranges for character data
    Local $baseAddresses[10] = [0x109BE1D0, 0x109CE1D0, 0x109DE1D0, 0x109EE1D0, 0x109FE1D0, 0x10A0E1D0, 0x10A1E1D0, 0x10A2E1D0, 0x10A3E1D0, 0x10A4E1D0]
    
    For $i = 0 To UBound($baseAddresses) - 1
        Local $testAddr = $baseAddresses[$i]
        Local $hpValue = ReadDWORD($handle, $testAddr + 272) ; HP offset
        Local $maxHpValue = ReadDWORD($handle, $testAddr + 268) ; Max HP offset
        
        If $hpValue > 0 And $maxHpValue > 0 And $hpValue <= $maxHpValue And $maxHpValue < 100000 Then
            GUICtrlSetData($editResults, "Potential Character Base: 0x" & Hex($testAddr) & " (HP: " & $hpValue & "/" & $maxHpValue & ")" & @CRLF, 1)
        EndIf
    Next
    
    _MemClose($handle)
    GUICtrlSetData($lblStatus, "Status: Character base scan complete")
EndFunc

Func ScanForMonsterTarget()
    GUICtrlSetData($lblStatus, "Status: Scanning for Monster Target Address...")
    GUICtrlSetData($editResults, "Scanning for Monster Target Address..." & @CRLF, 1)
    
    ; Get Tantra process
    Local $process = "HTLauncher.exe"
    Local $pid = ProcessExists($process)
    
    If $pid = 0 Then
        GUICtrlSetData($editResults, "Error: HTLauncher.exe not found!" & @CRLF, 1)
        Return
    EndIf
    
    Local $handle = _MemOpen(0x1F0FFF, False, $pid)
    If $handle = 0 Then
        GUICtrlSetData($editResults, "Error: Could not open process memory." & @CRLF, 1)
        Return
    EndIf
    
    ; Scan for monster target addresses (usually near character base)
    Local $baseAddresses[10] = [0x109BE280, 0x109CE280, 0x109DE280, 0x109EE280, 0x109FE280, 0x10A0E280, 0x10A1E280, 0x10A2E280, 0x10A3E280, 0x10A4E280]
    
    For $i = 0 To UBound($baseAddresses) - 1
        Local $testAddr = $baseAddresses[$i]
        Local $nameData = ReadString($handle, $testAddr, 20)
        
        If StringLen($nameData) > 2 And StringLen($nameData) < 20 Then
            GUICtrlSetData($editResults, "Potential Monster Target: 0x" & Hex($testAddr) & " (Name: " & $nameData & ")" & @CRLF, 1)
        EndIf
    Next
    
    _MemClose($handle)
    GUICtrlSetData($lblStatus, "Status: Monster target scan complete")
EndFunc

Func ScanAddressRange()
    GUICtrlSetData($lblStatus, "Status: Scanning address range...")
    GUICtrlSetData($editResults, "Scanning broader address range (this may take a while)..." & @CRLF, 1)
    
    Local $process = "HTLauncher.exe"
    Local $pid = ProcessExists($process)
    
    If $pid = 0 Then
        GUICtrlSetData($editResults, "Error: HTLauncher.exe not found!" & @CRLF, 1)
        Return
    EndIf
    
    Local $handle = _MemOpen(0x1F0FFF, False, $pid)
    If $handle = 0 Then
        GUICtrlSetData($editResults, "Error: Could not open process memory." & @CRLF, 1)
        Return
    EndIf
    
    ; Scan a broader range
    For $addr = 0x10900000 To 0x10B00000 Step 0x1000
        Local $hpValue = ReadDWORD($handle, $addr + 272)
        Local $maxHpValue = ReadDWORD($handle, $addr + 268)
        
        If $hpValue > 0 And $maxHpValue > 0 And $hpValue <= $maxHpValue And $maxHpValue < 100000 Then
            GUICtrlSetData($editResults, "Found HP data at: 0x" & Hex($addr) & " (HP: " & $hpValue & "/" & $maxHpValue & ")" & @CRLF, 1)
        EndIf
        
        ; Update status every 1000 iterations
        If Mod($addr, 0x10000) = 0 Then
            GUICtrlSetData($lblStatus, "Status: Scanning... 0x" & Hex($addr))
        EndIf
    Next
    
    _MemClose($handle)
    GUICtrlSetData($lblStatus, "Status: Range scan complete")
EndFunc

Func ReadDWORD($handle, $address)
    Local $data = _MemRead($handle, $address, 4)
    If @error Then Return 0
    Return BinaryToNumber($data, 1) ; 1 = little endian DWORD
EndFunc

Func ReadString($handle, $address, $maxLen)
    Local $data = _MemRead($handle, $address, $maxLen)
    If @error Then Return ""
    Return BinaryToString($data)
EndFunc

Func BinaryToNumber($binary, $flag)
    Local $struct = DllStructCreate("dword")
    DllStructSetData($struct, 1, $binary)
    Return DllStructGetData($struct, 1)
EndFunc
