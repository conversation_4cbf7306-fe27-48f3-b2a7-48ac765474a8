; <AUT2EXE VERSION: 3.2.3.0>

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Documents and Settings\All Users\Documents\Tantra\agit.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Documents and Settings\All Users\Documents\Tantra\mem.au3>
; ----------------------------------------------------------------------------

Func _MemRead($i_hProcess, $i_lpBaseAddress, $i_nSize, $v_lpNumberOfBytesRead = '')
    Local $v_Struct = DllStructCreate ('byte[' & $i_nSize & ']')
    DllCall('kernel32.dll', 'int', 'ReadProcessMemory', 'int', $i_hProcess, 'int', $i_lpBaseAddress, 'int', DllStructGetPtr ($v_Struct, 1), 'int', $i_nSize, 'int', $v_lpNumberOfBytesRead)
    Local $v_Return = DllStructGetData ($v_Struct, 1)
    $v_Struct=0
    Return $v_Return
EndFunc ;==> _MemRead()

Func _MemWrite($i_hProcess, $i_lpBaseAddress, $v_Inject, $i_nSize, $v_lpNumberOfBytesRead = '')
    Local $v_Struct = DllStructCreate ('byte[' & $i_nSize & ']')
    DllStructSetData ($v_Struct, 1, $v_Inject)
    $i_Call = DllCall('kernel32.dll', 'int', 'WriteProcessMemory', 'int', $i_hProcess, 'int', $i_lpBaseAddress, 'int', DllStructGetPtr ($v_Struct, 1), 'int', $i_nSize, 'int', $v_lpNumberOfBytesRead)
    $v_Struct=0
    Return $i_Call[0]
EndFunc ;==> _MemWrite()

Func _MemOpen($i_dwDesiredAccess, $i_bInheritHandle, $i_dwProcessId)
    $ai_Handle = DllCall("kernel32.dll", 'int', 'OpenProcess', 'int', $i_dwDesiredAccess, 'int', $i_bInheritHandle, 'int', $i_dwProcessId)
    If @error Then
        SetError(1)
        Return 0
    EndIf
    Return $ai_Handle[0]
EndFunc ;==> _MemOpen()

Func _MemClose($i_hProcess)
    $av_CloseHandle = DllCall('kernel32.dll', 'int', 'CloseHandle', 'int', $i_hProcess)
    Return $av_CloseHandle[0]
EndFunc ;==> _MemClose()


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Documents and Settings\All Users\Documents\Tantra\mem.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\GUIConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    Stub file providing compatibility between the new
;						library design and the old.
;
; ------------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\GUIDefaultConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Language:       English
; Description:    AutoIt-GUI default control styles.
;
; ------------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\WindowsConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    Windows constants.
;
; ------------------------------------------------------------------------------

; Window Styles
Global Const $WS_TILED				= 0
Global Const $WS_OVERLAPPED 		= 0
Global Const $WS_MAXIMIZEBOX		= 0x00010000
Global Const $WS_MINIMIZEBOX		= 0x00020000
Global Const $WS_TABSTOP			= 0x00010000
Global Const $WS_GROUP				= 0x00020000
Global Const $WS_SIZEBOX			= 0x00040000
Global Const $WS_THICKFRAME			= 0x00040000
Global Const $WS_SYSMENU			= 0x00080000
Global Const $WS_HSCROLL			= 0x00100000
Global Const $WS_VSCROLL			= 0x00200000
Global Const $WS_DLGFRAME 			= 0x00400000
Global Const $WS_BORDER				= 0x00800000
Global Const $WS_CAPTION			= 0x00C00000
Global Const $WS_OVERLAPPEDWINDOW	= 0x00CF0000
Global Const $WS_TILEDWINDOW		= 0x00CF0000
Global Const $WS_MAXIMIZE			= 0x01000000
Global Const $WS_CLIPCHILDREN		= 0x02000000
Global Const $WS_CLIPSIBLINGS		= 0x04000000
Global Const $WS_DISABLED 			= 0x08000000
Global Const $WS_VISIBLE			= 0x10000000
Global Const $WS_MINIMIZE			= 0x20000000
Global Const $WS_CHILD				= 0x40000000
Global Const $WS_POPUP				= 0x80000000
Global Const $WS_POPUPWINDOW		= 0x80880000

; Dialog Styles
Global Const $DS_MODALFRAME 		= 0x80
Global Const $DS_SETFOREGROUND		= 0x00000200
Global Const $DS_CONTEXTHELP		= 0x00002000

; Window Extended Styles
Global Const $WS_EX_ACCEPTFILES			= 0x00000010
Global Const $WS_EX_MDICHILD			= 0x00000040
Global Const $WS_EX_APPWINDOW			= 0x00040000
Global Const $WS_EX_CLIENTEDGE			= 0x00000200
Global Const $WS_EX_CONTEXTHELP			= 0x00000400
Global Const $WS_EX_DLGMODALFRAME 		= 0x00000001
Global Const $WS_EX_LEFTSCROLLBAR 		= 0x00004000
Global Const $WS_EX_OVERLAPPEDWINDOW	= 0x00000300
Global Const $WS_EX_RIGHT				= 0x00001000
Global Const $WS_EX_STATICEDGE			= 0x00020000
Global Const $WS_EX_TOOLWINDOW			= 0x00000080
Global Const $WS_EX_TOPMOST				= 0x00000008
Global Const $WS_EX_TRANSPARENT			= 0x00000020
Global Const $WS_EX_WINDOWEDGE			= 0x00000100
Global Const $WS_EX_LAYERED				= 0x00080000

; Messages
Global Const $WM_SIZE = 0x05
Global Const $WM_SIZING = 0x0214
Global Const $WM_USER = 0X400
Global Const $WM_GETTEXTLENGTH = 0x000E
Global Const $WM_GETTEXT = 0x000D

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\WindowsConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\AVIConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    AVI Constants.
;
; ------------------------------------------------------------------------------

; Styles
Global Const $ACS_CENTER			= 1
Global Const $ACS_TRANSPARENT		= 2
Global Const $ACS_AUTOPLAY			= 4
Global Const $ACS_TIMER				= 8
Global Const $ACS_NONTRANSPARENT	= 16

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\AVIConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\ComboConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    ComboBox constants.
;
; ------------------------------------------------------------------------------
; Combo
Global Const $CBS_SIMPLE			= 0x0001
Global Const $CBS_DROPDOWN			= 0x0002
Global Const $CBS_DROPDOWNLIST		= 0x0003
Global Const $CBS_AUTOHSCROLL		= 0x0040
Global Const $CBS_OEMCONVERT		= 0x0080
Global Const $CBS_SORT				= 0x0100
Global Const $CBS_NOINTEGRALHEIGHT	= 0x0400
Global Const $CBS_DISABLENOSCROLL	= 0x0800
Global Const $CBS_UPPERCASE			= 0x2000
Global Const $CBS_LOWERCASE			= 0x4000

; Error checking
Global Const $CB_ERR = -1
Global Const $CB_ERRATTRIBUTE = -3
Global Const $CB_ERRREQUIRED = -4
Global Const $CB_ERRSPACE = -2
Global Const $CB_OKAY = 0

; Messages to send to combobox
Global Const $CB_ADDSTRING = 0x143
Global Const $CB_DELETESTRING = 0x144
Global Const $CB_DIR = 0x145
Global Const $CB_FINDSTRING = 0x14C
Global Const $CB_FINDSTRINGEXACT = 0x158
Global Const $CB_GETCOUNT = 0x146
Global Const $CB_GETCURSEL = 0x147
Global Const $CB_GETDROPPEDCONTROLRECT = 0x152
Global Const $CB_GETDROPPEDSTATE = 0x157
Global Const $CB_GETDROPPEDWIDTH = 0X15f
Global Const $CB_GETEDITSEL = 0x140
Global Const $CB_GETEXTENDEDUI = 0x156
Global Const $CB_GETHORIZONTALEXTENT = 0x15d
Global Const $CB_GETITEMDATA = 0x150
Global Const $CB_GETITEMHEIGHT = 0x154
Global Const $CB_GETLBTEXT = 0x148
Global Const $CB_GETLBTEXTLEN = 0x149
Global Const $CB_GETLOCALE = 0x15A
Global Const $CB_GETMINVISIBLE = 0x1702
Global Const $CB_GETTOPINDEX = 0x15b
Global Const $CB_INITSTORAGE = 0x161
Global Const $CB_LIMITTEXT = 0x141
Global Const $CB_RESETCONTENT = 0x14B
Global Const $CB_INSERTSTRING = 0x14A
Global Const $CB_SELECTSTRING = 0x14D
Global Const $CB_SETCURSEL = 0x14E
Global Const $CB_SETDROPPEDWIDTH = 0x160
Global Const $CB_SETEDITSEL = 0x142
Global Const $CB_SETEXTENDEDUI = 0x155
Global Const $CB_SETHORIZONTALEXTENT = 0x15e
Global Const $CB_SETITEMDATA = 0x151
Global Const $CB_SETITEMHEIGHT = 0x153
Global Const $CB_SETLOCALE = 0x15
Global Const $CB_SETMINVISIBLE = 0x1701
Global Const $CB_SETTOPINDEX = 0x15c
Global Const $CB_SHOWDROPDOWN = 0x14F

; attributes
Global Const $CB_DDL_ARCHIVE = 0x20
Global Const $CB_DDL_DIRECTORY = 0x10
Global Const $CB_DDL_DRIVES = 0x4000
Global Const $CB_DDL_EXCLUSIVE = 0x8000
Global Const $CB_DDL_HIDDEN = 0x2
Global Const $CB_DDL_READONLY = 0x1
Global Const $CB_DDL_READWRITE = 0x0
Global Const $CB_DDL_SYSTEM = 0x4

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\ComboConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\DateTimeConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.1.1 (beta)
; Language:       English
; Description:    DateTime Control Constants.
;
; ------------------------------------------------------------------------------

; Date
Global Const $DTS_SHORTDATEFORMAT	= 0
Global Const $DTS_UPDOWN			= 1
Global Const $DTS_SHOWNONE			= 2
Global Const $DTS_LONGDATEFORMAT	= 4
Global Const $DTS_TIMEFORMAT		= 9
Global Const $DTS_RIGHTALIGN		= 32

; MonthCal
Global Const $MCS_NOTODAY			= 16
Global Const $MCS_NOTODAYCIRCLE		= 8
Global Const $MCS_WEEKNUMBERS		= 4

Global Const $MCM_FIRST = 0x1000
Global Const $MCM_GETCOLOR = ($MCM_FIRST + 11)
Global Const $MCM_GETFIRSTDAYOFWEEK = ($MCM_FIRST + 16)
Global Const $MCM_GETMAXSELCOUNT = ($MCM_FIRST + 3)
Global Const $MCM_GETMAXTODAYWIDTH = ($MCM_FIRST + 21)
Global Const $MCM_GETMINREQRECT = ($MCM_FIRST + 9)
Global Const $MCM_GETMONTHDELTA = ($MCM_FIRST + 19)
Global Const $MCS_MULTISELECT = 0x2
Global Const $MCM_SETCOLOR = ($MCM_FIRST + 10)
Global Const $MCM_SETFIRSTDAYOFWEEK = ($MCM_FIRST + 15)
Global Const $MCM_SETMAXSELCOUNT = ($MCM_FIRST + 4)
Global Const $MCM_SETMONTHDELTA = ($MCM_FIRST + 20)

Global Const $MCSC_BACKGROUND = 0
Global Const $MCSC_MONTHBK = 4
Global Const $MCSC_TEXT = 1
Global Const $MCSC_TITLEBK = 2
Global Const $MCSC_TITLETEXT = 3
Global Const $MCSC_TRAILINGTEXT = 5

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\DateTimeConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\EditConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Language:       English
; Description:    Edit Constants.
;
; ------------------------------------------------------------------------------

; Styles
Global Const $ES_LEFT				= 0
Global Const $ES_CENTER				= 1
Global Const $ES_RIGHT				= 2
Global Const $ES_MULTILINE			= 4
Global Const $ES_UPPERCASE			= 8
Global Const $ES_LOWERCASE			= 16
Global Const $ES_PASSWORD			= 32
Global Const $ES_AUTOVSCROLL		= 64
Global Const $ES_AUTOHSCROLL		= 128
Global Const $ES_NOHIDESEL			= 256
Global Const $ES_OEMCONVERT			= 1024
Global Const $ES_READONLY			= 2048
Global Const $ES_WANTRETURN			= 4096
Global Const $ES_NUMBER				= 8192
;Global Const $ES_DISABLENOSCROLL = 8192
;Global Const $ES_SUNKEN = 16384
;Global Const $ES_VERTICAL = 4194304
;Global Const $ES_SELECTIONBAR = 16777216

; Error checking
Global Const $EC_ERR = -1

; Messages to send to edit control
Global Const $ECM_FIRST = 0X1500
Global Const $EM_CANUNDO = 0xC6
Global Const $EM_EMPTYUNDOBUFFER = 0xCD
Global Const $EM_GETFIRSTVISIBLELINE = 0xCE
Global Const $EM_GETLINE = 0xC4
Global Const $EM_GETLINECOUNT = 0xBA
Global Const $EM_GETMODIFY = 0xB8
Global Const $EM_GETRECT = 0xB2
Global Const $EM_GETSEL = 0xB0
Global Const $EM_LINEFROMCHAR = 0xC9
Global Const $EM_LINEINDEX = 0xBB
Global Const $EM_LINELENGTH = 0xC1
Global Const $EM_LINESCROLL = 0xB6
Global Const $EM_REPLACESEL = 0xC2
Global Const $EM_SCROLL = 0xB5
Global Const $EM_SCROLLCARET = 0x00B7
Global Const $EM_SETMODIFY = 0xB9
Global Const $EM_SETSEL = 0xB1
Global Const $EM_UNDO = 0xC7
Global Const $EM_SETREADONLY = 0x00CF
Global Const $EM_SETTABSTOPS = 0x00CB

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\EditConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\StaticConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    Static (label, pic, icon) Constants.
;
; ------------------------------------------------------------------------------

; Label/Pic/Icon
Global Const $SS_LEFT			= 0
Global Const $SS_CENTER			= 1
Global Const $SS_RIGHT			= 2
Global Const $SS_ICON			= 3
Global Const $SS_BLACKRECT		= 4
Global Const $SS_GRAYRECT		= 5
Global Const $SS_WHITERECT		= 6
Global Const $SS_BLACKFRAME		= 7
Global Const $SS_GRAYFRAME		= 8
Global Const $SS_WHITEFRAME		= 9
Global Const $SS_SIMPLE			= 11
Global Const $SS_LEFTNOWORDWRAP	= 12
Global Const $SS_BITMAP			= 15
Global Const $SS_ETCHEDHORZ		= 16
Global Const $SS_ETCHEDVERT		= 17
Global Const $SS_ETCHEDFRAME	= 18
Global Const $SS_NOPREFIX		= 0x0080
Global Const $SS_NOTIFY			= 0x0100
Global Const $SS_CENTERIMAGE	= 0x0200
Global Const $SS_RIGHTJUST		= 0x0400
Global Const $SS_SUNKEN			= 0x1000

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\StaticConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\ListBoxConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    ListBox Constants.
;
; ------------------------------------------------------------------------------

; Styles
Global Const $LBS_NOTIFY			= 0x0001
Global Const $LBS_SORT				= 0x0002
Global Const $LBS_USETABSTOPS		= 0x0080
Global Const $LBS_NOINTEGRALHEIGHT	= 0x0100
Global Const $LBS_DISABLENOSCROLL	= 0x1000
Global Const $LBS_NOSEL				= 0x4000
Global Const $LBS_STANDARD			= 0xA00003

; Errors
Global Const $LB_ERR = -1
Global Const $LB_ERRATTRIBUTE = -3
Global Const $LB_ERRREQUIRED = -4
Global Const $LB_ERRSPACE = -2

; Messages to send to listbox
Global Const $LB_ADDSTRING = 0x180
Global Const $LB_DELETESTRING = 0x182
Global Const $LB_DIR = 0x18D
Global Const $LB_FINDSTRING = 0x18F
Global Const $LB_FINDSTRINGEXACT = 0x1A2
Global Const $LB_GETANCHORINDEX = 0x019D
Global Const $LB_GETCARETINDEX = 0x019F
Global Const $LB_GETCOUNT = 0x18B
Global Const $LB_GETCURSEL = 0x188
Global Const $LB_GETHORIZONTALEXTENT = 0x193
Global Const $LB_GETITEMRECT = 0x198
Global Const $LB_GETLISTBOXINFO = 0x01B2
Global Const $LB_GETLOCALE = 0x1A6
Global Const $LB_GETSEL = 0x0187
Global Const $LB_GETSELCOUNT = 0x0190
Global Const $LB_GETSELITEMS = 0X191
Global Const $LB_GETTEXT = 0x0189
Global Const $LB_GETTEXTLEN = 0x018A
Global Const $LB_GETTOPINDEX = 0x018E
Global Const $LB_INSERTSTRING = 0x181
Global Const $LB_RESETCONTENT = 0x184
Global Const $LB_SELECTSTRING = 0x18C
Global Const $LB_SETITEMHEIGHT = 0x1A0
Global Const $LB_SELITEMRANGE = 0x19B
Global Const $LB_SELITEMRANGEEX = 0x0183
Global Const $LB_SETANCHORINDEX = 0x19C
Global Const $LB_SETCARETINDEX = 0x19E
Global Const $LB_SETCURSEL = 0x186
Global Const $LB_SETHORIZONTALEXTENT = 0x194
Global Const $LB_SETLOCALE = 0x1A5
Global Const $LB_SETSEL = 0x0185
Global Const $LB_SETTOPINDEX = 0x197

Global Const $LBS_MULTIPLESEL = 0x8

; attributes
Global Const $DDL_ARCHIVE = 0x20
Global Const $DDL_DIRECTORY = 0x10
Global Const $DDL_DRIVES = 0x4000
Global Const $DDL_EXCLUSIVE = 0x8000
Global Const $DDL_HIDDEN = 0x2
Global Const $DDL_READONLY = 0x1
Global Const $DDL_READWRITE = 0x0
Global Const $DDL_SYSTEM = 0x4

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\ListBoxConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\ListViewConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    ListView Constants.
;
; ------------------------------------------------------------------------------

; Styles
Global Const $LVS_ICON	 			= 0x0000
Global Const $LVS_REPORT 			= 0x0001
Global Const $LVS_SMALLICON			= 0x0002
Global Const $LVS_LIST				= 0x0003
Global Const $LVS_EDITLABELS		= 0x0200
Global Const $LVS_NOCOLUMNHEADER	= 0x4000
Global Const $LVS_NOSORTHEADER		= 0x8000
Global Const $LVS_SINGLESEL			= 0x0004
Global Const $LVS_SHOWSELALWAYS		= 0x0008
Global Const $LVS_SORTASCENDING		= 0X0010
Global Const $LVS_SORTDESCENDING	= 0x0020
Global Const $LVS_NOLABELWRAP		= 0x0080

; listView Extended Styles
Global Const $LVS_EX_FULLROWSELECT		= 0x00000020
Global Const $LVS_EX_GRIDLINES			= 0x00000001
Global Const $LVS_EX_SUBITEMIMAGES		= 0x00000002
Global Const $LVS_EX_CHECKBOXES			= 0x00000004
Global Const $LVS_EX_TRACKSELECT		= 0x00000008
Global Const $LVS_EX_HEADERDRAGDROP		= 0x00000010
Global Const $LVS_EX_FLATSB				= 0x00000100
Global Const $LVS_EX_BORDERSELECT		= 0x00008000
;Global Const $LVS_EX_MULTIWORKAREAS		= 0x00002000
;Global Const $LVS_EX_SNAPTOGRID			= 0x00080000
;Global Const $LVS_EX_DOUBLEBUFFER		= 0x00010000
Global Const $LVS_EX_HIDELABELS = 0x20000
Global Const $LVS_EX_INFOTIP = 0x400
Global Const $LVS_EX_LABELTIP = 0x4000
Global Const $LVS_EX_ONECLICKACTIVATE = 0x40
Global Const $LVS_EX_REGIONAL = 0x200
Global Const $LVS_EX_SINGLEROW = 0x40000
Global Const $LVS_EX_TWOCLICKACTIVATE = 0x80
;~ Global Const $LVS_EX_TRACKSELECT = 0x8
Global Const $LVS_EX_UNDERLINEHOT = 0x800
Global Const $LVS_EX_UNDERLINECOLD = 0x1000

; error
Global Const $LV_ERR = -1


; Messages to send to listview
Global Const $CCM_FIRST = 0x2000
Global Const $CCM_GETUNICODEFORMAT = ($CCM_FIRST + 6)
Global Const $CCM_SETUNICODEFORMAT = ($CCM_FIRST + 5)
Global Const $CLR_NONE = 0xFFFFFFFF
Global Const $LVM_FIRST = 0x1000

Global Const $LV_VIEW_DETAILS = 0x1
Global Const $LV_VIEW_ICON = 0x0
Global Const $LV_VIEW_LIST = 0x3
Global Const $LV_VIEW_SMALLICON = 0x2
Global Const $LV_VIEW_TILE = 0x4

Global Const $LVCF_FMT = 0x1
Global Const $LVCF_WIDTH = 0x2
Global Const $LVCF_TEXT = 0x4
Global Const $LVCFMT_CENTER = 0x2
Global Const $LVCFMT_LEFT = 0x0
Global Const $LVCFMT_RIGHT = 0x1

Global Const $LVA_ALIGNLEFT = 0x1
Global Const $LVA_ALIGNTOP = 0x2
Global Const $LVA_DEFAULT = 0x0
Global Const $LVA_SNAPTOGRID = 0x5

Global Const $LVIF_STATE = 0x8
Global Const $LVIF_TEXT = 0x1

Global Const $LVFI_PARAM = 0x1
Global Const $LVFI_PARTIAL = 0x8
Global Const $LVFI_STRING = 0x2
Global Const $LVFI_WRAP = 0x20

Global Const $VK_LEFT = 0x25
Global Const $VK_RIGHT = 0x27
Global Const $VK_UP = 0x26
Global Const $VK_DOWN = 0x28
Global Const $VK_END = 0x23
Global Const $VK_PRIOR = 0x21
Global Const $VK_NEXT = 0x22

Global Const $LVIR_BOUNDS = 0

Global Const $LVIS_CUT = 0x4
Global Const $LVIS_DROPHILITED = 0x8
Global Const $LVIS_FOCUSED = 0x1
Global Const $LVIS_OVERLAYMASK = 0xF00
Global Const $LVIS_SELECTED = 0x2
Global Const $LVIS_STATEIMAGEMASK = 0xF000

Global Const $LVM_ARRANGE = ($LVM_FIRST + 22)
Global Const $LVM_CANCELEDITLABEL = ($LVM_FIRST + 179)
Global Const $LVM_DELETECOLUMN = 0x101C
Global Const $LVM_DELETEITEM = 0x1008
Global Const $LVM_DELETEALLITEMS = 0x1009
Global Const $LVM_EDITLABELA = ($LVM_FIRST + 23)
Global Const $LVM_EDITLABEL = $LVM_EDITLABELA
Global Const $LVM_ENABLEGROUPVIEW = ($LVM_FIRST + 157)
Global Const $LVM_ENSUREVISIBLE = ($LVM_FIRST + 19)
Global Const $LVM_FINDITEM = ($LVM_FIRST + 13)
Global Const $LVM_GETBKCOLOR = ($LVM_FIRST + 0)
Global Const $LVM_GETCALLBACKMASK = ($LVM_FIRST + 10)
Global Const $LVM_GETCOLUMNORDERARRAY = ($LVM_FIRST + 59)
Global Const $LVM_GETCOLUMNWIDTH = ($LVM_FIRST + 29)
Global Const $LVM_GETCOUNTPERPAGE = ($LVM_FIRST + 40)
Global Const $LVM_GETEDITCONTROL = ($LVM_FIRST + 24)
Global Const $LVM_GETEXTENDEDLISTVIEWSTYLE = ($LVM_FIRST + 55)
Global Const $LVM_GETHEADER = ($LVM_FIRST + 31)
Global Const $LVM_GETHOTCURSOR = ($LVM_FIRST + 63)
Global Const $LVM_GETHOTITEM = ($LVM_FIRST + 61)
Global Const $LVM_GETHOVERTIME = ($LVM_FIRST + 72)
Global Const $LVM_GETIMAGELIST = ($LVM_FIRST + 2)
Global Const $LVM_GETITEMA = ($LVM_FIRST + 5)
Global Const $LVM_GETITEMCOUNT = 0x1004
Global Const $LVM_GETITEMSTATE = ($LVM_FIRST + 44)
Global Const $LVM_GETITEMTEXTA = ($LVM_FIRST + 45);
Global Const $LVM_GETNEXTITEM = 0x100c
Global Const $LVM_GETSELECTEDCOLUMN = ($LVM_FIRST + 174)
Global Const $LVM_GETSELECTEDCOUNT = ($LVM_FIRST + 50)
Global Const $LVM_GETSUBITEMRECT = ($LVM_FIRST + 56);
Global Const $LVM_GETTOPINDEX = ($LVM_FIRST + 39)
Global Const $LVM_GETUNICODEFORMAT = $CCM_GETUNICODEFORMAT
Global Const $LVM_GETVIEW = ($LVM_FIRST + 143)
Global Const $LVM_GETVIEWRECT = ($LVM_FIRST + 34)
Global Const $LVM_INSERTCOLUMNA = ($LVM_FIRST + 27)
Global Const $LVM_INSERTITEMA = ($LVM_FIRST + 7)
Global Const $LVM_REDRAWITEMS = ($LVM_FIRST + 21)
Global Const $LVM_SETUNICODEFORMAT = $CCM_SETUNICODEFORMAT
Global Const $LVM_SCROLL = ($LVM_FIRST + 20)
Global Const $LVM_SETBKCOLOR = 0x1001
Global Const $LVM_SETCALLBACKMASK = ($LVM_FIRST + 11)
Global Const $LVM_SETCOLUMNA = ($LVM_FIRST + 26)
Global Const $LVM_SETCOLUMNORDERARRAY = ($LVM_FIRST + 58)
Global Const $LVM_SETCOLUMNWIDTH = 0x101E
Global Const $LVM_SETEXTENDEDLISTVIEWSTYLE = 0x1036
Global Const $LVM_SETHOTITEM = ($LVM_FIRST + 60)
Global Const $LVM_SETHOVERTIME = ($LVM_FIRST + 71)
Global Const $LVM_SETICONSPACING = ($LVM_FIRST + 53)
Global Const $LVM_SETITEMCOUNT = ($LVM_FIRST + 47)
Global Const $LVM_SETITEMPOSITION = ($LVM_FIRST + 15)
Global Const $LVM_SETITEMSTATE = ($LVM_FIRST + 43)
Global Const $LVM_SETITEMTEXTA = ($LVM_FIRST + 46)
Global Const $LVM_SETSELECTEDCOLUMN = ($LVM_FIRST + 140)
Global Const $LVM_SETTEXTCOLOR = ($LVM_FIRST + 36)
Global Const $LVM_SETTEXTBKCOLOR = ($LVM_FIRST + 38)
Global Const $LVM_SETVIEW = ($LVM_FIRST + 142)
Global Const $LVM_UPDATE = ($LVM_FIRST + 42)

Global Const $LVNI_ABOVE = 0x100
Global Const $LVNI_BELOW = 0x200
Global Const $LVNI_TOLEFT = 0x400
Global Const $LVNI_TORIGHT = 0x800
Global Const $LVNI_ALL = 0x0
Global Const $LVNI_CUT = 0x4
Global Const $LVNI_DROPHILITED = 0x8
Global Const $LVNI_FOCUSED = 0x1
Global Const $LVNI_SELECTED = 0x2

Global Const $LVSCW_AUTOSIZE = -1
Global Const $LVSCW_AUTOSIZE_USEHEADER = -2

Global Const $LVSICF_NOINVALIDATEALL = 0x1
Global Const $LVSICF_NOSCROLL = 0x2

Global Const $LVSIL_NORMAL = 0
Global Const $LVSIL_SMALL = 1
Global Const $LVSIL_STATE = 2

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\ListViewConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\SliderConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    Slider Constants
;
; ------------------------------------------------------------------------------

; Styles
Global Const $TBS_AUTOTICKS	= 0x0001
Global Const $TBS_VERT		= 0x0002
Global Const $TBS_HORZ		= 0x0000
Global Const $TBS_TOP		= 0x0004
Global Const $TBS_BOTTOM	= 0x0000
Global Const $TBS_LEFT		= 0x0004
Global Const $TBS_RIGHT		= 0x0000
Global Const $TBS_BOTH		= 0x0008
Global Const $TBS_NOTICKS	= 0x0010
Global Const $TBS_NOTHUMB	= 0x0080

; Messages
Global Const $TWM_USER = 0x400	; WM_USER
Global Const $TBM_CLEARTICS = ($TWM_USER + 9)
Global Const $TBM_GETLINESIZE = ($TWM_USER + 24)
Global Const $TBM_GETPAGESIZE = ($TWM_USER + 22)
Global Const $TBM_GETNUMTICS = ($TWM_USER + 16)
Global Const $TBM_GETPOS = $TWM_USER
Global Const $TBM_GETRANGEMAX = ($TWM_USER + 2)
Global Const $TBM_GETRANGEMIN = ($TWM_USER + 1)
Global Const $TBM_SETLINESIZE = ($TWM_USER + 23)
Global Const $TBM_SETPAGESIZE = ($TWM_USER + 21)
Global Const $TBM_SETPOS = ($TWM_USER + 5)
Global Const $TBM_SETTICFREQ = ($TWM_USER + 20)

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\SliderConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\TreeViewConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    TreeView Constants.
;
; ------------------------------------------------------------------------------

; Styles
Global Const $TVS_HASBUTTONS     	= 0x0001
Global Const $TVS_HASLINES       	= 0x0002
Global Const $TVS_LINESATROOT    	= 0x0004
;Global Const $TVS_EDITLABELS      = 0x0008
Global Const $TVS_DISABLEDRAGDROP	= 0x0010
Global Const $TVS_SHOWSELALWAYS		= 0x0020
;Global Const $TVS_RTLREADING     = 0x0040
Global Const $TVS_NOTOOLTIPS		= 0x0080
Global Const $TVS_CHECKBOXES		= 0x0100
Global Const $TVS_TRACKSELECT		= 0x0200
Global Const $TVS_SINGLEEXPAND		= 0x0400
;Global Const $TVS_INFOTIP        = 0x0800
Global Const $TVS_FULLROWSELECT		= 0x1000
Global Const $TVS_NOSCROLL			= 0x2000
Global Const $TVS_NONEVENHEIGHT		= 0x4000

Global Const $TVE_COLLAPSE			= 0x0001
Global Const $TVE_EXPAND			= 0x0002
Global Const $TVE_TOGGLE			= 0x0003
Global Const $TVE_EXPANDPARTIAL		= 0x4000
Global Const $TVE_COLLAPSERESET = 0x8000

Global Const $TVGN_ROOT				= 0x0000
Global Const $TVGN_NEXT				= 0x0001
Global Const $TVGN_PARENT			= 0x0003
Global Const $TVGN_CHILD			= 0x0004
Global Const $TVGN_CARET			= 0x0009

Global Const $TVI_ROOT				= 0xFFFF0000
Global Const $TVI_FIRST				= 0xFFFF0001
Global Const $TVI_LAST				= 0xFFFF0002
Global Const $TVI_SORT				= 0xFFFF0003

Global Const $TVIF_TEXT = 0x0001
Global Const $TVIF_IMAGE			= 0x0002
Global Const $TVIF_PARAM			= 0x0004
Global Const $TVIF_STATE			= 0x0008
Global Const $TVIF_HANDLE			= 0x0010
Global Const $TVIF_SELECTEDIMAGE	= 0x0020
Global Const $TVIF_CHILDREN			= 0x0040

Global Const $TVIS_SELECTED			= 0x0002
Global Const $TVIS_CUT				= 0x0004
Global Const $TVIS_DROPHILITED		= 0x0008
Global Const $TVIS_BOLD				= 0x0010
Global Const $TVIS_EXPANDED			= 0x0020
Global Const $TVIS_EXPANDEDONCE		= 0x0040
Global Const $TVIS_EXPANDPARTIAL	= 0x0080
Global Const $TVIS_OVERLAYMASK		= 0x0F00
Global Const $TVIS_STATEIMAGEMASK = 0xF000

; Messages to send to TreeView
Global Const $TV_FIRST				= 0x1100
Global Const $TVM_INSERTITEM		= $TV_FIRST + 0
Global Const $TVM_DELETEITEM		= $TV_FIRST + 1
Global Const $TVM_EXPAND			= $TV_FIRST + 2
Global Const $TVM_GETCOUNT			= $TV_FIRST + 5
Global Const $TVM_GETINDENT			= $TV_FIRST + 6
Global Const $TVM_SETINDENT			= $TV_FIRST + 7
Global Const $TVM_GETIMAGELIST		= $TV_FIRST + 8
Global Const $TVM_SETIMAGELIST		= $TV_FIRST + 9
Global Const $TVM_GETNEXTITEM		= $TV_FIRST + 10
Global Const $TVM_SELECTITEM		= $TV_FIRST + 11
Global Const $TVM_GETITEM			= $TV_FIRST + 12
Global Const $TVM_SETITEM			= $TV_FIRST + 13
Global Const $TVM_SORTCHILDREN		= $TV_FIRST + 19
Global Const $TVM_ENSUREVISIBLE		= $TV_FIRST + 20
Global Const $TVM_SETBKCOLOR		= $TV_FIRST + 29
Global Const $TVM_SETTEXTCOLOR		= $TV_FIRST + 30
Global Const $TVM_GETBKCOLOR		= $TV_FIRST + 31
Global Const $TVM_GETTEXTCOLOR		= $TV_FIRST + 32
Global Const $TVM_SETLINECOLOR		= $TV_FIRST + 40
Global Const $TVM_GETLINECOLOR		= $TV_FIRST + 41

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\TreeViewConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\UpDownConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    UpDown Constants.
;
; ------------------------------------------------------------------------------

; Styles
Global Const $UDS_WRAP 				= 0x0001
Global Const $UDS_SETBUDDYINT		= 0x0002
Global Const $UDS_ALIGNRIGHT 		= 0x0004
Global Const $UDS_ALIGNLEFT			= 0x0008
Global Const $UDS_ARROWKEYS 		= 0x0020
Global Const $UDS_HORZ 				= 0x0040
Global Const $UDS_NOTHOUSANDS 		= 0x0080

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\UpDownConstants.au3>
; ----------------------------------------------------------------------------


; Control default styles
Global Const $GUI_SS_DEFAULT_AVI		= $ACS_TRANSPARENT
Global Const $GUI_SS_DEFAULT_BUTTON		= 0
Global Const $GUI_SS_DEFAULT_CHECKBOX	= 0
Global Const $GUI_SS_DEFAULT_COMBO		= BitOR($CBS_DROPDOWN, $CBS_AUTOHSCROLL, $WS_VSCROLL)
Global Const $GUI_SS_DEFAULT_DATE		= $DTS_LONGDATEFORMAT
Global Const $GUI_SS_DEFAULT_EDIT		= BitOR($ES_WANTRETURN, $WS_VSCROLL, $WS_HSCROLL, $ES_AUTOVSCROLL, $ES_AUTOHSCROLL)
Global Const $GUI_SS_DEFAULT_GRAPHIC	= 0
Global Const $GUI_SS_DEFAULT_GROUP		= 0
Global Const $GUI_SS_DEFAULT_ICON		= $SS_NOTIFY
Global Const $GUI_SS_DEFAULT_INPUT		= BitOR($ES_LEFT, $ES_AUTOHSCROLL)
Global Const $GUI_SS_DEFAULT_LABEL		= 0
Global Const $GUI_SS_DEFAULT_LIST		= BitOR($LBS_SORT, $WS_BORDER, $WS_VSCROLL, $LBS_NOTIFY)
Global Const $GUI_SS_DEFAULT_LISTVIEW	= BitOR($LVS_SHOWSELALWAYS, $LVS_SINGLESEL)
Global Const $GUI_SS_DEFAULT_MONTHCAL	= 0
Global Const $GUI_SS_DEFAULT_PIC		= $SS_NOTIFY
Global Const $GUI_SS_DEFAULT_PROGRESS	= 0
Global Const $GUI_SS_DEFAULT_RADIO		= 0
Global Const $GUI_SS_DEFAULT_SLIDER		= $TBS_AUTOTICKS
Global Const $GUI_SS_DEFAULT_TAB		= 0
Global Const $GUI_SS_DEFAULT_TREEVIEW	= BitOR($TVS_HASBUTTONS, $TVS_HASLINES, $TVS_LINESATROOT, $TVS_DISABLEDRAGDROP, $TVS_SHOWSELALWAYS)
Global Const $GUI_SS_DEFAULT_UPDOWN		= $UDS_ALIGNRIGHT
Global Const $GUI_SS_DEFAULT_GUI		= BitOR($WS_MINIMIZEBOX, $WS_CAPTION, $WS_POPUP, $WS_SYSMENU)

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\GUIDefaultConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\GUIConstantsEx.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    Constants to be used in GUI applications.
;
; ------------------------------------------------------------------------------


; Events and messages
Global Const $GUI_EVENT_CLOSE			= -3
Global Const $GUI_EVENT_MINIMIZE		= -4
Global Const $GUI_EVENT_RESTORE			= -5
Global Const $GUI_EVENT_MAXIMIZE		= -6
Global Const $GUI_EVENT_PRIMARYDOWN		= -7
Global Const $GUI_EVENT_PRIMARYUP		= -8
Global Const $GUI_EVENT_SECONDARYDOWN	= -9
Global Const $GUI_EVENT_SECONDARYUP		= -10
Global Const $GUI_EVENT_MOUSEMOVE		= -11
Global Const $GUI_EVENT_RESIZED			= -12
Global Const $GUI_EVENT_DROPPED			= -13

Global Const $GUI_RUNDEFMSG				= 'GUI_RUNDEFMSG'

; State
Global Const $GUI_AVISTOP		= 0
Global Const $GUI_AVISTART		= 1
Global Const $GUI_AVICLOSE		= 2

Global Const $GUI_CHECKED		= 1
Global Const $GUI_INDETERMINATE	= 2
Global Const $GUI_UNCHECKED		= 4

Global Const $GUI_DROPACCEPTED	= 8
Global Const $GUI_NODROPACCEPTED = 4096
Global Const $GUI_ACCEPTFILES	= $GUI_DROPACCEPTED	; to be suppressed

Global Const $GUI_SHOW			= 16
Global Const $GUI_HIDE 			= 32
Global Const $GUI_ENABLE		= 64
Global Const $GUI_DISABLE		= 128

Global Const $GUI_FOCUS			= 256
Global Const $GUI_NOFOCUS		= 8192
Global Const $GUI_DEFBUTTON		= 512

Global Const $GUI_EXPAND		= 1024
Global Const $GUI_ONTOP			= 2048


; Font
Global Const $GUI_FONTITALIC	= 2
Global Const $GUI_FONTUNDER		= 4
Global Const $GUI_FONTSTRIKE	= 8


; Resizing
Global Const $GUI_DOCKAUTO			= 0x0001
Global Const $GUI_DOCKLEFT			= 0x0002
Global Const $GUI_DOCKRIGHT			= 0x0004
Global Const $GUI_DOCKHCENTER		= 0x0008
Global Const $GUI_DOCKTOP			= 0x0020
Global Const $GUI_DOCKBOTTOM		= 0x0040
Global Const $GUI_DOCKVCENTER		= 0x0080
Global Const $GUI_DOCKWIDTH			= 0x0100
Global Const $GUI_DOCKHEIGHT		= 0x0200

Global Const $GUI_DOCKSIZE			= 0x0300	; width+height
Global Const $GUI_DOCKMENUBAR		= 0x0220	; top+height
Global Const $GUI_DOCKSTATEBAR		= 0x0240	; bottom+height
Global Const $GUI_DOCKALL			= 0x0322	; left+top+width+height
Global Const $GUI_DOCKBORDERS		= 0x0066	; left+top+right+bottom

; Graphic
Global Const $GUI_GR_CLOSE		= 1
Global Const $GUI_GR_LINE		= 2
Global Const $GUI_GR_BEZIER		= 4
Global Const $GUI_GR_MOVE		= 6
Global Const $GUI_GR_COLOR		= 8
Global Const $GUI_GR_RECT		= 10
Global Const $GUI_GR_ELLIPSE	= 12
Global Const $GUI_GR_PIE		= 14
Global Const $GUI_GR_DOT		= 16
Global Const $GUI_GR_PIXEL		= 18
Global Const $GUI_GR_HINT		= 20
Global Const $GUI_GR_REFRESH	= 22
Global Const $GUI_GR_PENSIZE	= 24
Global Const $GUI_GR_NOBKCOLOR	= -2

; Background color special flags
Global Const $GUI_BKCOLOR_DEFAULT = -1
Global Const $GUI_BKCOLOR_TRANSPARENT = -2
Global Const $GUI_BKCOLOR_LV_ALTERNATE = 0xFE000000

; Other
Global Const $GUI_WS_EX_PARENTDRAG =      0x00100000

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\GUIConstantsEx.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\WindowsConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\WindowsConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\ComboConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\ComboConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\ListViewConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\ListViewConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\StaticConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\StaticConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\ButtonConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    Button (Group, Radio, Checkbox, Button) Constants.
;
; ------------------------------------------------------------------------------

; Group
Global Const $BS_GROUPBOX		= 0x0007

; Button
Global Const $BS_BOTTOM			= 0x0800
Global Const $BS_CENTER			= 0x0300
Global Const $BS_DEFPUSHBUTTON	= 0x0001
Global Const $BS_LEFT			= 0x0100
Global Const $BS_MULTILINE		= 0x2000
Global Const $BS_PUSHBOX		= 0x000A
Global Const $BS_PUSHLIKE		= 0x1000
Global Const $BS_RIGHT			= 0x0200
Global Const $BS_RIGHTBUTTON	= 0x0020
Global Const $BS_TOP			= 0x0400
Global Const $BS_VCENTER		= 0x0C00
Global Const $BS_FLAT			= 0x8000
Global Const $BS_ICON			= 0x0040
Global Const $BS_BITMAP			= 0x0080
Global Const $BS_NOTIFY			= 0x4000

; Checkbox
Global Const $BS_3STATE			= 0x0005
Global Const $BS_AUTO3STATE		= 0x0006
Global Const $BS_AUTOCHECKBOX	= 0x0003
Global Const $BS_CHECKBOX		= 0x0002

; Radio
Global Const $BS_AUTORADIOBUTTON	= 0x0009

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\ButtonConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\ListBoxConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\ListBoxConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\TabConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    Tab Constants.
;
; ------------------------------------------------------------------------------
; Styles
Global Const $TCS_SCROLLOPPOSITE	= 0x0001
Global Const $TCS_BOTTOM			= 0x0002
Global Const $TCS_RIGHT				= 0x0002
Global Const $TCS_MULTISELECT		= 0x0004
Global Const $TCS_FLATBUTTONS		= 0x0008
Global Const $TCS_FORCEICONLEFT		= 0x0010
Global Const $TCS_FORCELABELLEFT	= 0x0020
Global Const $TCS_HOTTRACK			= 0x0040
Global Const $TCS_VERTICAL			= 0x0080
Global Const $TCS_TABS				= 0x0000
Global Const $TCS_BUTTONS			= 0x0100
Global Const $TCS_SINGLELINE		= 0x0000
Global Const $TCS_MULTILINE			= 0x0200
Global Const $TCS_RIGHTJUSTIFY		= 0x0000
Global Const $TCS_FIXEDWIDTH		= 0x0400
Global Const $TCS_RAGGEDRIGHT		= 0x0800
Global Const $TCS_FOCUSONBUTTONDOWN = 0x1000
Global Const $TCS_OWNERDRAWFIXED	= 0x2000
Global Const $TCS_TOOLTIPS			= 0x4000
Global Const $TCS_FOCUSNEVER		= 0x8000

; Tab Extended Styles
Global Const $TCS_EX_FLATSEPARATORS 	= 0x1
;Global Const $TCS_EX_REGISTERDROP 		= 0x2

; Error checking
Global Const $TC_ERR = -1

; event(s)
Global Const $TCIS_BUTTONPRESSED = 0x1

; extended styles
;~ Global Const $TCS_EX_FLATSEPARATORS = 0x1
Global Const $TCS_EX_REGISTERDROP = 0x2

; Messages to send to Tab control
Global Const $TCM_FIRST = 0x1300
Global Const $TCM_DELETEALLITEMS = ($TCM_FIRST + 9)
Global Const $TCM_DELETEITEM = ($TCM_FIRST + 8)
Global Const $TCM_DESELECTALL = ($TCM_FIRST + 50)
Global Const $TCM_GETCURFOCUS = ($TCM_FIRST + 47)
Global Const $TCM_GETCURSEL = ($TCM_FIRST + 11)
Global Const $TCM_GETEXTENDEDSTYLE = ($TCM_FIRST + 53)
Global Const $TCM_GETITEMCOUNT = ($TCM_FIRST + 4)
Global Const $TCM_GETITEMRECT = ($TCM_FIRST + 10)
Global Const $TCM_GETROWCOUNT = ($TCM_FIRST + 44)
Global Const $TCM_SETITEMSIZE = $TCM_FIRST + 41

Global Const $TCCM_FIRST = 0X2000
Global Const $TCCM_GETUNICODEFORMAT = ($TCCM_FIRST + 6)
Global Const $TCM_GETUNICODEFORMAT = $TCCM_GETUNICODEFORMAT

Global Const $TCM_HIGHLIGHTITEM = ($TCM_FIRST + 51)
Global Const $TCM_SETCURFOCUS = ($TCM_FIRST + 48)
Global Const $TCM_SETCURSEL = ($TCM_FIRST + 12)
Global Const $TCM_SETMINTABWIDTH = ($TCM_FIRST + 49)
Global Const $TCM_SETPADDING = ($TCM_FIRST + 43)

Global Const $TCCM_SETUNICODEFORMAT = ($TCCM_FIRST + 5)
Global Const $TCM_SETUNICODEFORMAT = $TCCM_SETUNICODEFORMAT

Global Const $TCN_FIRST = -550
Global Const $TCN_SELCHANGE = ($TCN_FIRST - 1)
Global Const $TCN_SELCHANGING = ($TCN_FIRST - 2)

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\TabConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\EditConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\EditConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\DateTimeConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\DateTimeConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\SliderConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\SliderConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\TreeViewConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\TreeViewConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\ProgressConstants.au3>
; ----------------------------------------------------------------------------


; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.2
; Description:    Progress Constants.
;
; ------------------------------------------------------------------------------

; Styles
Global Const $PBS_SMOOTH	= 1
Global Const $PBS_VERTICAL	= 4

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\ProgressConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\AVIConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\AVIConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\UpDownConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\UpDownConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\GUIConstants.au3>
; ----------------------------------------------------------------------------


; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-START: C:\Program Files\AutoIt3\beta\Include\Date.au3>
; ----------------------------------------------------------------------------

;
; ------------------------------------------------------------------------------
;
; AutoIt Version: 3.0
; Language:       English
; Description:    Functions that assist with dates and times.
;
;===============================================================================
;
; Description:      Calculates a new date based on a given date and add an interval.
; Parameter(s):     $sType    D = Add number of days to the given date
;                             M = Add number of months to the given date
;                             Y = Add number of years to the given date
;                             w = Add number of Weeks to the given date
;                             h = Add number of hours to the given date
;                             n = Add number of minutes to the given date
;                             s = Add number of seconds to the given date
;                   $iValToAdd - number to be added
;                   $sDate    - Input date in the format YYYY/MM/DD[ HH:MM:SS]
; Requirement(s):   None
; Return Value(s):  On Success - Date newly calculated date.
;                   On Failure - 0  and Set
;                                   @ERROR to:  1 - Invalid $sType
;                                                  2 - Invalid $iValToAdd
;                                                  3 - Invalid $sDate
; Author(s):        Jos van der Zande
; Note(s):          The function will not return an invalid date.
;                   When 3 months is are to '2004/1/31' then the result will be 2004/04/30
;
;
;===============================================================================
Func _DateAdd($sType, $iValToAdd, $sDate)
	Local $asTimePart[4]
	Local $asDatePart[4]
	Local $iJulianDate
	Local $iTimeVal
	Local $iNumDays
	Local $Day2Add
	; Verify that $sType is Valid
	$sType = StringLeft($sType, 1)
	If StringInStr("D,M,Y,w,h,n,s", $sType) = 0 Or $sType = "" Then
		SetError(1)
		Return (0)
	EndIf
	; Verify that Value to Add  is Valid
	If Not StringIsInt($iValToAdd) Then
		SetError(2)
		Return (0)
	EndIf
	; Verify If InputDate is valid
	If Not _DateIsValid($sDate) Then
		SetError(3)
		Return (0)
	EndIf
	; split the date and time into arrays
	_DateTimeSplit($sDate, $asDatePart, $asTimePart)

	; ====================================================
	; adding days then get the julian date
	; add the number of day
	; and convert back to Gregorian
	If $sType = "d" Or $sType = "w" Then
		If $sType = "w" Then $iValToAdd = $iValToAdd * 7
		$iJulianDate = _DateToDayValue($asDatePart[1], $asDatePart[2], $asDatePart[3]) + $iValToAdd
		_DayValueToDate($iJulianDate, $asDatePart[1], $asDatePart[2], $asDatePart[3])
	EndIf
	; ====================================================
	; adding Months
	If $sType = "m" Then
		$asDatePart[2] = $asDatePart[2] + $iValToAdd
		; pos number of months
		While $asDatePart[2] > 12
			$asDatePart[2] = $asDatePart[2] - 12
			$asDatePart[1] = $asDatePart[1] + 1
		WEnd
		; Neg number of months
		While $asDatePart[2] < 1
			$asDatePart[2] = $asDatePart[2] + 12
			$asDatePart[1] = $asDatePart[1] - 1
		WEnd
	EndIf
	; ====================================================
	; adding Years
	If $sType = "y" Then
		$asDatePart[1] = $asDatePart[1] + $iValToAdd
	EndIf
	; ====================================================
	; adding Time value
	If $sType = "h" Or $sType = "n" Or $sType = "s" Then
		$iTimeVal = _TimeToTicks($asTimePart[1], $asTimePart[2], $asTimePart[3]) / 1000
		If $sType = "h" Then $iTimeVal = $iTimeVal + $iValToAdd * 3600
		If $sType = "n" Then $iTimeVal = $iTimeVal + $iValToAdd * 60
		If $sType = "s" Then $iTimeVal = $iTimeVal + $iValToAdd
		; calculated days to add
		$Day2Add = Int($iTimeVal/ (24 * 60 * 60))
		$iTimeVal = $iTimeVal - $Day2Add * 24 * 60 * 60
		If $iTimeVal < 0 Then
			$Day2Add = $Day2Add - 1
			$iTimeVal = $iTimeVal + 24 * 60 * 60
		EndIf
		$iJulianDate = _DateToDayValue($asDatePart[1], $asDatePart[2], $asDatePart[3]) + $Day2Add
		; calculate the julian back to date
		_DayValueToDate($iJulianDate, $asDatePart[1], $asDatePart[2], $asDatePart[3])
		; caluculate the new time
		_TicksToTime($iTimeVal * 1000, $asTimePart[1], $asTimePart[2], $asTimePart[3])
	EndIf
	; ====================================================
	; check if the Input day is Greater then the new month last day.
	; if so then change it to the last possible day in the month
	$iNumDays = StringSplit('31,28,31,30,31,30,31,31,30,31,30,31', ',')
	If _DateIsLeapYear($asDatePart[1]) Then $iNumDays[2] = 29
	;
	If $iNumDays[$asDatePart[2]] < $asDatePart[3] Then $asDatePart[3] = $iNumDays[$asDatePart[2]]
	; ========================
	; Format the return date
	; ========================
	; Format the return date
	$sDate = $asDatePart[1] & '/' & StringRight("0" & $asDatePart[2], 2) & '/' & StringRight("0" & $asDatePart[3], 2)
	; add the time when specified in the input
	If $asTimePart[0] > 0 Then
		If $asTimePart[0] > 2 Then
			$sDate = $sDate & " " & StringRight("0" & $asTimePart[1], 2) & ':' & StringRight("0" & $asTimePart[2], 2) & ':' & StringRight("0" & $asTimePart[3], 2)
		Else
			$sDate = $sDate & " " & StringRight("0" & $asTimePart[1], 2) & ':' & StringRight("0" & $asTimePart[2], 2)
		EndIf
	EndIf
	;
	Return ($sDate)
EndFunc   ;==>_DateAdd

;===============================================================================
;
; Description:      Returns the name of the weekday, based on the specified day.
; Parameter(s):     $iDayNum - Day number
;                   $iShort  - Format:
;                              0 = Long name of the weekday
;                              1 = Abbreviated name of the weekday
; Requirement(s):   None
; Return Value(s):  On Success - Weekday name
;                   On Failure - A NULL string and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          English only
;
;===============================================================================
Func _DateDayOfWeek($iDayNum, $iShort = 0)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $aDayOfWeek[8]

	$aDayOfWeek[1] = "Sunday"
	$aDayOfWeek[2] = "Monday"
	$aDayOfWeek[3] = "Tuesday"
	$aDayOfWeek[4] = "Wednesday"
	$aDayOfWeek[5] = "Thursday"
	$aDayOfWeek[6] = "Friday"
	$aDayOfWeek[7] = "Saturday"
	Select
		Case Not StringIsInt($iDayNum) Or Not StringIsInt($iShort)
			SetError(1)
			Return ""
		Case $iDayNum < 1 Or $iDayNum > 7
			SetError(1)
			Return ""
		Case Else
			Select
				Case $iShort = 0
					Return $aDayOfWeek[$iDayNum]
				Case $iShort = 1
					Return StringLeft($aDayOfWeek[$iDayNum], 3)
				Case Else
					SetError(1)
					Return ""
			EndSelect
	EndSelect
EndFunc   ;==>_DateDayOfWeek

;===============================================================================
;
; Function Name:  _DateDaysInMonth()
; Description:    Returns the number of days in a month, based on the specified
;                 month and year.
; Author(s):      Jeremy Landes <jlandes at landeserve dot com>
;
;===============================================================================
Func _DateDaysInMonth($iYear, $iMonthNum)
	Local $aiNumDays
	$aiNumDays = "31,28,31,30,31,30,31,31,30,31,30,31"
	$aiNumDays = StringSplit($aiNumDays, ",")
	If _DateIsMonth($iMonthNum) And _DateIsYear($iYear) Then
		If _DateIsLeapYear($iYear) Then $aiNumDays[2] = $aiNumDays[2] + 1
		SetError(0)
		Return $aiNumDays[$iMonthNum]
	Else
		SetError(1)
		Return 0
	EndIf
EndFunc   ;==>_DateDaysInMonth

;===============================================================================
;
; Description:      Returns the difference between 2 dates, expressed in the type requested
; Parameter(s):     $sType - returns the difference in:
;                               d = days
;                               m = Months
;                               y = Years
;                               w = Weeks
;                               h = Hours
;                               n = Minutes
;                               s = Seconds
;                   $sStartDate    - Input Start date in the format "YYYY/MM/DD[ HH:MM:SS]"
;                   $sEndDate    - Input End date in the format "YYYY/MM/DD[ HH:MM:SS]"
; Requirement(s):   None
; Return Value(s):  On Success - Difference between the 2 dates
;                   On Failure - 0  and Set
;                                   @ERROR to:  1 - Invalid $sType
;                                               2 - Invalid $sStartDate
;                                               3 - Invalid $sEndDate
; Author(s):        Jos van der Zande
; Note(s):
;
;===============================================================================
Func _DateDiff($sType, $sStartDate, $sEndDate)
	Local $asStartDatePart[4]
	Local $asStartTimePart[4]
	Local $asEndDatePart[4]
	Local $asEndTimePart[4]
	Local $iTimeDiff
	Local $iYearDiff
	Local $iMonthDiff
	Local $iStartTimeInSecs
	Local $iEndTimeInSecs
	Local $aDaysDiff
	;
	; Verify that $sType is Valid
	$sType = StringLeft($sType, 1)
	If StringInStr("d,m,y,w,h,n,s", $sType) = 0 Or $sType = "" Then
		SetError(1)
		Return (0)
	EndIf
	; Verify If StartDate is valid
	If Not _DateIsValid($sStartDate) Then
		SetError(2)
		Return (0)
	EndIf
	; Verify If EndDate is valid
	If Not _DateIsValid($sEndDate) Then
		SetError(3)
		Return (0)
	EndIf
	; split the StartDate and Time into arrays
	_DateTimeSplit($sStartDate, $asStartDatePart, $asStartTimePart)
	; split the End  Date and time into arrays
	_DateTimeSplit($sEndDate, $asEndDatePart, $asEndTimePart)
	; ====================================================
	; Get the differens in days between the 2 dates
	$aDaysDiff = _DateToDayValue($asEndDatePart[1], $asEndDatePart[2], $asEndDatePart[3]) - _DateToDayValue($asStartDatePart[1], $asStartDatePart[2], $asStartDatePart[3])
	; ====================================================
	; Get the differens in Seconds between the 2 times when specified
	If $asStartTimePart[0] > 1 And $asEndTimePart[0] > 1 Then
		$iStartTimeInSecs = $asStartTimePart[1] * 3600 + $asStartTimePart[2] * 60 + $asStartTimePart[3]
		$iEndTimeInSecs = $asEndTimePart[1] * 3600 + $asEndTimePart[2] * 60 + $asEndTimePart[3]
		$iTimeDiff = $iEndTimeInSecs - $iStartTimeInSecs
		If $iTimeDiff < 0 Then
			$aDaysDiff = $aDaysDiff - 1
			$iTimeDiff = $iTimeDiff + 24 * 60 * 60
		EndIf
	Else
		$iTimeDiff = 0
	EndIf
	Select
		Case $sType = "d"
			Return ($aDaysDiff)
		Case $sType = "m"
			$iYearDiff = $asEndDatePart[1] - $asStartDatePart[1]
			$iMonthDiff = $asEndDatePart[2] - $asStartDatePart[2] + $iYearDiff * 12
			If $asEndDatePart[3] < $asStartDatePart[3] Then $iMonthDiff = $iMonthDiff - 1
			$iStartTimeInSecs = $asStartTimePart[1] * 3600 + $asStartTimePart[2] * 60 + $asStartTimePart[3]
			$iEndTimeInSecs = $asEndTimePart[1] * 3600 + $asEndTimePart[2] * 60 + $asEndTimePart[3]
			$iTimeDiff = $iEndTimeInSecs - $iStartTimeInSecs
			If $asEndDatePart[3] = $asStartDatePart[3] And $iTimeDiff < 0 Then $iMonthDiff = $iMonthDiff - 1
			Return ($iMonthDiff)
		Case $sType = "y"
			$iYearDiff = $asEndDatePart[1] - $asStartDatePart[1]
			If $asEndDatePart[2] < $asStartDatePart[2] Then $iYearDiff = $iYearDiff - 1
			If $asEndDatePart[2] = $asStartDatePart[2] And $asEndDatePart[3] < $asStartDatePart[3] Then $iYearDiff = $iYearDiff - 1
			$iStartTimeInSecs = $asStartTimePart[1] * 3600 + $asStartTimePart[2] * 60 + $asStartTimePart[3]
			$iEndTimeInSecs = $asEndTimePart[1] * 3600 + $asEndTimePart[2] * 60 + $asEndTimePart[3]
			$iTimeDiff = $iEndTimeInSecs - $iStartTimeInSecs
			If $asEndDatePart[2] = $asStartDatePart[2] And $asEndDatePart[3] = $asStartDatePart[3] And $iTimeDiff < 0 Then $iYearDiff = $iYearDiff - 1
			Return ($iYearDiff)
		Case $sType = "w"
			Return (Int($aDaysDiff / 7))
		Case $sType = "h"
			Return ($aDaysDiff * 24 + Int($iTimeDiff / 3600))
		Case $sType = "n"
			Return ($aDaysDiff * 24 * 60 + Int($iTimeDiff / 60))
		Case $sType = "s"
			Return ($aDaysDiff * 24 * 60 * 60 + $iTimeDiff)
	EndSelect
EndFunc   ;==>_DateDiff

;===============================================================================
;
; Description:      Returns 1 if the specified year falls on a leap year and
;                   returns 0 if it does not.
; Parameter(s):     $iYear - Year to check
; Requirement(s):   None
; Return Value(s):  On Success - 0 = Year is not a leap year
;                                1 = Year is a leap year
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          None
;
;===============================================================================
Func _DateIsLeapYear($iYear)
	If StringIsInt($iYear) Then
		Select
			Case Mod($iYear, 4) = 0 And Mod($iYear, 100) <> 0
				Return 1
			Case Mod($iYear, 400) = 0
				Return 1
			Case Else
				Return 0
		EndSelect
	Else
		SetError(1)
		Return 0
	EndIf
EndFunc   ;==>_DateIsLeapYear

;===============================================================================
;
; Function Name:  _DateIsMonth()
; Description:    Checks a given number to see if it is a valid month.
; Author(s):      Jeremy Landes <jlandes at landeserve dot com>
;
;===============================================================================
Func _DateIsMonth($iNumber)
	If StringIsInt($iNumber) Then
		If $iNumber >= 1 And $iNumber <= 12 Then
			Return 1
		Else
			Return 0
		EndIf
	Else
		Return 0
	EndIf
EndFunc   ;==>_DateIsMonth

;===============================================================================
;
; Description:      Verify if date and time are valid "yyyy/mm/dd[ hh:mm[:ss]]".
; Parameter(s):     $sDate format "yyyy/mm/dd[ hh:mm[:ss]]"
; Requirement(s):   None
; Return Value(s):  On Success - 1
;                   On Failure - 0
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
;                   Jos van der Zande <jdeb at autoitscript dot com>
; Note(s):          None
;
;===============================================================================
Func _DateIsValid($sDate)
	Local $asDatePart[4]
	Local $asTimePart[4]
	Local $iNumDays
	$iNumDays = "31,28,31,30,31,30,31,31,30,31,30,31"
	$iNumDays = StringSplit($iNumDays, ",")
	; split the date and time into arrays
	_DateTimeSplit($sDate, $asDatePart, $asTimePart)
	If $asDatePart[0] <> 3 Then
		Return (0)
	EndIf
	; verify valid input date values
	If _DateIsLeapYear($asDatePart[1]) Then $iNumDays[2] = 29
	If $asDatePart[1] < 1000 Or $asDatePart[1] > 2999 Then Return (0)
	If $asDatePart[2] < 1 Or $asDatePart[2] > 12 Then Return (0)
	If $asDatePart[3] < 1 Or $asDatePart[3] > $iNumDays[$asDatePart[2]] Then Return (0)

	; verify valid input Time values
	If $asTimePart[0] < 1 Then Return (1)    ; No time specified so date must be correct
	If $asTimePart[0] < 2 Then Return (0)    ; need at least HH:MM when something is specified
	If $asTimePart[1] < 0 Or $asTimePart[1] > 23 Then Return (0)
	If $asTimePart[2] < 0 Or $asTimePart[2] > 59 Then Return (0)
	If $asTimePart[3] < 0 Or $asTimePart[3] > 59 Then Return (0)
	; we got here so date/time must be good
	Return (1)
EndFunc   ;==>_DateIsValid

;===============================================================================
;
; Function Name:  _DateIsYear()
; Description:    Checks a given number to see if it is a valid year.
; Author(s):      Jeremy Landes <jlandes at landeserve dot com>
;
;===============================================================================
Func _DateIsYear($iNumber)
	If StringIsInt($iNumber) Then
		If StringLen($iNumber) = 4 Then
			Return 1
		Else
			Return 0
		EndIf
	Else
		Return 0
	EndIf
EndFunc   ;==>_DateIsYear

;===============================================================================
;
; Description:      Returns previous weekday number, based on the specified day
;                   of the week.
; Parameter(s):     $iWeekdayNum - Weekday number
; Requirement(s):   None
; Return Value(s):  On Success - Previous weekday number
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          None
;
;===============================================================================
Func _DateLastWeekdayNum($iWeekdayNum)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $iLastWeekdayNum

	Select
		Case Not StringIsInt($iWeekdayNum)
			SetError(1)
			Return 0
		Case $iWeekdayNum < 1 Or $iWeekdayNum > 7
			SetError(1)
			Return 0
		Case Else
			If $iWeekdayNum = 1 Then
				$iLastWeekdayNum = 7
			Else
				$iLastWeekdayNum = $iWeekdayNum - 1
			EndIf

			Return $iLastWeekdayNum
	EndSelect
EndFunc   ;==>_DateLastWeekdayNum

;===============================================================================
;
; Description:      Returns previous month number, based on the specified month.
; Parameter(s):     $iMonthNum - Month number
; Requirement(s):   None
; Return Value(s):  On Success - Previous month number
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          None
;
;===============================================================================
Func _DateLastMonthNum($iMonthNum)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $iLastMonthNum

	Select
		Case Not StringIsInt($iMonthNum)
			SetError(1)
			Return 0
		Case $iMonthNum < 1 Or $iMonthNum > 12
			SetError(1)
			Return 0
		Case Else
			If $iMonthNum = 1 Then
				$iLastMonthNum = 12
			Else
				$iLastMonthNum = $iMonthNum - 1
			EndIf

			$iLastMonthNum = StringFormat("%02d", $iLastMonthNum)
			Return $iLastMonthNum
	EndSelect
EndFunc   ;==>_DateLastMonthNum

;===============================================================================
;
; Description:      Returns previous month's year, based on the specified month
;                   and year.
; Parameter(s):     $iMonthNum - Month number
;                   $iYear     - Year
; Requirement(s):   None
; Return Value(s):  On Success - Previous month's year
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          None
;
;===============================================================================
Func _DateLastMonthYear($iMonthNum, $iYear)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $iLastYear

	Select
		Case Not StringIsInt($iMonthNum) Or Not StringIsInt($iYear)
			SetError(1)
			Return 0
		Case $iMonthNum < 1 Or $iMonthNum > 12
			SetError(1)
			Return 0
		Case Else
			If $iMonthNum = 1 Then
				$iLastYear = $iYear - 1
			Else
				$iLastYear = $iYear
			EndIf

			$iLastYear = StringFormat("%04d", $iLastYear)
			Return $iLastYear
	EndSelect
EndFunc   ;==>_DateLastMonthYear

;===============================================================================
;
; Description:      Returns the name of the month, based on the specified month.
; Parameter(s):     $iMonthNum - Month number
;                   $iShort    - Format:
;                                0 = Long name of the month
;                                1 = Abbreviated name of the month
; Requirement(s):   None
; Return Value(s):  On Success - Month name
;                   On Failure - A NULL string and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          English only
;
;===============================================================================
Func _DateMonthOfYear($iMonthNum, $iShort)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $aMonthOfYear[13]

	$aMonthOfYear[1] = "January"
	$aMonthOfYear[2] = "February"
	$aMonthOfYear[3] = "March"
	$aMonthOfYear[4] = "April"
	$aMonthOfYear[5] = "May"
	$aMonthOfYear[6] = "June"
	$aMonthOfYear[7] = "July"
	$aMonthOfYear[8] = "August"
	$aMonthOfYear[9] = "September"
	$aMonthOfYear[10] = "October"
	$aMonthOfYear[11] = "November"
	$aMonthOfYear[12] = "December"

	Select
		Case Not StringIsInt($iMonthNum) Or Not StringIsInt($iShort)
			SetError(1)
			Return ""
		Case $iMonthNum < 1 Or $iMonthNum > 12
			SetError(1)
			Return ""
		Case Else
			Select
				Case $iShort = 0
					Return $aMonthOfYear[$iMonthNum]
				Case $iShort = 1
					Return StringLeft($aMonthOfYear[$iMonthNum], 3)
				Case Else
					SetError(1)
					Return ""
			EndSelect
	EndSelect
EndFunc   ;==>_DateMonthOfYear

;===============================================================================
;
; Description:      Returns next weekday number, based on the specified day of
;                   the week.
; Parameter(s):     $iWeekdayNum - Weekday number
; Requirement(s):   None
; Return Value(s):  On Success - Next weekday number
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          None
;
;===============================================================================
Func _DateNextWeekdayNum($iWeekdayNum)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $iNextWeekdayNum

	Select
		Case Not StringIsInt($iWeekdayNum)
			SetError(1)
			Return 0
		Case $iWeekdayNum < 1 Or $iWeekdayNum > 7
			SetError(1)
			Return 0
		Case Else
			If $iWeekdayNum = 7 Then
				$iNextWeekdayNum = 1
			Else
				$iNextWeekdayNum = $iWeekdayNum + 1
			EndIf

			Return $iNextWeekdayNum
	EndSelect
EndFunc   ;==>_DateNextWeekdayNum

;===============================================================================
;
; Description:      Returns next month number, based on the specified month.
; Parameter(s):     $iMonthNum - Month number
; Requirement(s):   None
; Return Value(s):  On Success - Next month number
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          None
;
;===============================================================================
Func _DateNextMonthNum($iMonthNum)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $iNextMonthNum

	Select
		Case Not StringIsInt($iMonthNum)
			SetError(1)
			Return 0
		Case $iMonthNum < 1 Or $iMonthNum > 12
			SetError(1)
			Return 0
		Case Else
			If $iMonthNum = 12 Then
				$iNextMonthNum = 1
			Else
				$iNextMonthNum = $iMonthNum + 1
			EndIf

			$iNextMonthNum = StringFormat("%02d", $iNextMonthNum)
			Return $iNextMonthNum
	EndSelect
EndFunc   ;==>_DateNextMonthNum

;===============================================================================
;
; Description:      Returns next month's year, based on the specified month and
;                   year.
; Parameter(s):     $iMonthNum - Month number
;                   $iYear     - Year
; Requirement(s):   None
; Return Value(s):  On Success - Next month's year
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jeremy Landes <jlandes at landeserve dot com>
; Note(s):          None
;
;===============================================================================
Func _DateNextMonthYear($iMonthNum, $iYear)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $iNextYear

	Select
		Case Not StringIsInt($iMonthNum) Or Not StringIsInt($iYear)
			SetError(1)
			Return 0
		Case $iMonthNum < 1 Or $iMonthNum > 12
			SetError(1)
			Return 0
		Case Else
			If $iMonthNum = 12 Then
				$iNextYear = $iYear + 1
			Else
				$iNextYear = $iYear
			EndIf

			$iNextYear = StringFormat("%04d", $iNextYear)
			Return $iNextYear
	EndSelect
EndFunc   ;==>_DateNextMonthYear

;===============================================================================
;
; Description:      Split Date and Time into two separateArrays.
; Parameter(s):     $sDate format "yyyy/mm/dd[ hh:mm[:ss]]"
;                    or    format "yyyy/mm/dd[Thh:mm[:ss]]"
;                    or    format "yyyy-mm-dd[ hh:mm[:ss]]"
;                    or    format "yyyy-mm-dd[Thh:mm[:ss]]"
;                    or    format "yyyy.mm.dd[ hh:mm[:ss]]"
;                    or    format "yyyy.mm.dd[Thh:mm[:ss]]"
;                   $asDatePart[4] array that contains the Date
;                   $iTimePart[4] array that contains the Time
; Requirement(s):   None
; Return Value(s):  Always 1
; Author(s):        Jos van der Zande
; Note(s):          Its expected you first do a _DateIsValid( $sDate ) for the input
;
;===============================================================================
Func _DateTimeSplit($sDate, ByRef $asDatePart, ByRef $iTimePart)
	Local $sDateTime
	Local $x
	; split the Date and Time portion
	$sDateTime = StringSplit($sDate, " T")
	; split the date portion
	If $sDateTime[0] > 0 Then $asDatePart = StringSplit($sDateTime[1], "/-.")
	; split the Time portion
	If $sDateTime[0] > 1 Then
		$iTimePart = StringSplit($sDateTime[2], ":")
		If UBound($iTimePart) < 4 Then ReDim $iTimePart[4]
	Else
		Dim $iTimePart[4]
	EndIf
	; Ensure the arrays contain 4 values
	If UBound($asDatePart) < 4 Then ReDim $asDatePart[4]
	; update the array to contain numbers not strings
	For $x = 1 To 3
		If StringIsInt($asDatePart[$x]) Then
			$asDatePart[$x] = Number($asDatePart[$x])
		Else
			$asDatePart[$x] = -1
		EndIf
		If StringIsInt($iTimePart[$x]) Then
			$iTimePart[$x] = Number($iTimePart[$x])
		Else
			$iTimePart[$x] = -1
		EndIf
	Next
	Return (1)
EndFunc   ;==>_DateTimeSplit

;===============================================================================
;
; Description:      Returns the number of days since noon 4713 BC January 1.
; Parameter(s):     $Year  - Year in format YYYY
;                   $Month - Month in format MM
;                   $sDate - Day of the month format DD
; Requirement(s):   None
; Return Value(s):  On Success - Returns the Juliandate
;                   On Failure - 0  and sets @ERROR = 1
; Author(s):        Jos van der Zande / Jeremy Landes
; Note(s):          None
;
;===============================================================================
Func _DateToDayValue($iYear, $iMonth, $iDay)
	Local $i_aFactor
	Local $i_bFactor
	Local $i_cFactor
	Local $i_eFactor
	Local $i_fFactor
	Local $iJulianDate
	; Verify If InputDate is valid
	If Not _DateIsValid(StringFormat("%04d/%02d/%02d", $iYear, $iMonth, $iDay)) Then
		SetError(1)
		Return ("")
	EndIf
	If $iMonth < 3 Then
		$iMonth = $iMonth + 12
		$iYear = $iYear - 1
	EndIf
	$i_aFactor = Int($iYear / 100)
	$i_bFactor = Int($i_aFactor / 4)
	$i_cFactor = 2 - $i_aFactor + $i_bFactor
	$i_eFactor = Int(1461 * ($iYear + 4716) / 4)
	$i_fFactor = Int(153 * ($iMonth + 1) / 5)
	$iJulianDate = $i_cFactor + $iDay + $i_eFactor + $i_fFactor - 1524.5
	Return ($iJulianDate)
EndFunc   ;==>_DateToDayValue

;===============================================================================
;
; Description:      Returns the DayofWeek number for a given Date.  1=Sunday
; Parameter(s):     $Year
;                   $Month
;                   $day
; Requirement(s):   None
; Return Value(s):  On Success - Returns Day of the Week Range is 1 to 7 where 1=Sunday.
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jos van der Zande <jdeb at autoitscript dot com>
; Note(s):          None
;
;===============================================================================
Func _DateToDayOfWeek($iYear, $iMonth, $iDay)
	Local $i_aFactor
	Local $i_yFactor
	Local $i_mFactor
	Local $i_dFactor
	; Verify If InputDate is valid
	If Not _DateIsValid($iYear & "/" & $iMonth & "/" & $iDay) Then
		SetError(1)
		Return ("")
	EndIf
	$i_aFactor = Int((14 - $iMonth) / 12)
	$i_yFactor = $iYear - $i_aFactor
	$i_mFactor = $iMonth + (12 * $i_aFactor) - 2
	$i_dFactor = Mod($iDay + $i_yFactor + Int($i_yFactor / 4) - Int($i_yFactor / 100) + Int($i_yFactor / 400) + Int((31 * $i_mFactor) / 12), 7)
	Return ($i_dFactor + 1)
EndFunc   ;==>_DateToDayOfWeek

;===============================================================================
;
; Description:      Returns the DayofWeek number for a given Date.  0=Monday 6=Sunday
; Parameter(s):     $Year
;                   $Month
;                   $day
; Requirement(s):   None
; Return Value(s):  On Success - Returns Day of the Week Range is 0 to 6 where 0=Monday.
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Jos van der Zande <jdeb at autoitscript dot com>
; Note(s):          None
;
;===============================================================================
Func _DateToDayOfWeekISO($iYear, $iMonth, $iDay)
	Local $idow = _DateToDayOfWeek($iYear, $iMonth, $iDay)
	If @error Then
		SetError(1)
		Return ""
	EndIf
	If $idow >= 2 Then Return $idow - 2
	Return 6
EndFunc   ;==>_DateToDayOfWeekISO

;===============================================================================
;
; Description:      Add the given days since noon 4713 BC January 1 and return the date.
; Parameter(s):     $iJulianDate    - Julian date number
;                   $Year  - Year in format YYYY
;                   $Month - Month in format MM
;                   $sDate - Day of the month format DD
; Requirement(s):   None
; Return Value(s):  On Success - Returns the Date in the parameter vars
;                   On Failure - 0  and sets @ERROR = 1
; Author(s):        Jos van der Zande
; Note(s):          None
;
;===============================================================================
Func _DayValueToDate($iJulianDate, ByRef $iYear, ByRef $iMonth, ByRef $iDay)
	Local $i_zFactor
	Local $i_wFactor
	Local $i_aFactor
	Local $i_bFactor
	Local $i_xFactor
	Local $i_cFactor
	Local $i_dFactor
	Local $i_eFactor
	Local $i_fFactor
	; check for valid input date
	If $iJulianDate < 0 Or Not IsNumber($iJulianDate) Then
		SetError(1)
		Return 0
	EndIf
	; calculte the date
	$i_zFactor = Int($iJulianDate + 0.5)
	$i_wFactor = Int(($i_zFactor - 1867216.25) / 36524.25)
	$i_xFactor = Int($i_wFactor / 4)
	$i_aFactor = $i_zFactor + 1 + $i_wFactor - $i_xFactor
	$i_bFactor = $i_aFactor + 1524
	$i_cFactor = Int(($i_bFactor - 122.1) / 365.25)
	$i_dFactor = Int(365.25 * $i_cFactor)
	$i_eFactor = Int(($i_bFactor - $i_dFactor) / 30.6001)
	$i_fFactor = Int(30.6001 * $i_eFactor)
	$iDay = $i_bFactor - $i_dFactor - $i_fFactor
	; (must get number less than or equal to 12)
	If $i_eFactor - 1 < 13 Then
		$iMonth = $i_eFactor - 1
	Else
		$iMonth = $i_eFactor - 13
	EndIf
	If $iMonth < 3 Then
		$iYear = $i_cFactor - 4715    ; (if Month is January or February)
	Else
		$iYear = $i_cFactor - 4716    ;(otherwise)
	EndIf
	$iYear = StringFormat("%04d", $iYear)
	$iMonth = StringFormat("%02d", $iMonth)
	$iDay = StringFormat("%02d", $iDay)
	Return $iYear & "/" & $iMonth & "/" & $iDay
EndFunc   ;==>_DayValueToDate

;===============================================================================
;
; Description:      Returns the date in the PC's regional settings format.
; Parameter(s):     $date - format "YYYY/MM/DD"
;                   $sType - :
;                      0 - Display a date and/or time. If there is a date part, display it as a short date.
;                          If there is a time part, display it as a long time. If present, both parts are displayed.
;                      1 - Display a date using the long date format specified in your computer's regional settings.
;                      2 - Display a date using the short date format specified in your computer's regional settings.
;                      3 - Display a time using the time format specified in your computer's regional settings.
;                      4 - Display a time using the 24-hour format (hh:mm).
; Requirement(s):   None
; Return Value(s):  On Success - Returns date in proper format
;                   On Failure - 0  and Set
;                                   @ERROR to:  1 - Invalid $sDate
;                                               2 - Invalid $sType
; Author(s):        Jos van der Zande <jdeb at autoitscript dot com>
; Note(s):          None...
;
;===============================================================================
Func _DateTimeFormat($sDate, $sType)
	Local $asDatePart[4]
	Local $asTimePart[4]
	Local $sTempDate = ""
	Local $sTempTime = ""
	Local $sAM
	Local $sPM
	Local $iWday
	Local $lngX
	; Verify If InputDate is valid
	If Not _DateIsValid($sDate) Then
		SetError(1)
		Return ("")
	EndIf
	; input validation
	If $sType < 0 Or $sType > 5 Or Not IsInt($sType) Then
		SetError(2)
		Return ("")
	EndIf
	; split the date and time into arrays
	_DateTimeSplit($sDate, $asDatePart, $asTimePart)
	;
	; 	Const $LOCALE_USER_DEFAULT = 0x400
	;   Const $LOCALE_SDATE = 0x1D            ;  date separator
	;   Const $LOCALE_STIME = 0x1E            ;  time separator
	;   Const $LOCALE_S1159 = 0x28            ;  AM designator
	;   Const $LOCALE_S2359 = 0x29            ;  PM designator
	; 	Const $LOCALE_SSHORTDATE = 0x1F       ;  short date format string
	; 	Const $LOCALE_SLONGDATE = 0x20        ;  long date format string
	; 	Const $LOCALE_STIMEFORMAT = 0x1003    ;  time format string

	Switch $sType
		Case 0
			; Get ShortDate format
			$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x1F, "str", "", "long", 255)
			If Not @error And $lngX[0] <> 0 Then
				$sTempDate = $lngX[3]
			Else
				$sTempDate = "M/d/yyyy"
			EndIf
			;
			; Get Time format
			If $asTimePart[0] > 1 Then
				$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x1003, "str", "", "long", 255)
				If Not @error And $lngX[0] <> 0 Then
					$sTempTime = $lngX[3]
				Else
					$sTempTime = "h:mm:ss tt"
				EndIf
			EndIf
		Case 1
			; Get LongDate format
			$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x20, "str", "", "long", 255)
			If Not @error And $lngX[0] <> 0 Then
				$sTempDate = $lngX[3]
			Else
				$sTempDate = "dddd, MMMM dd, yyyy"
			EndIf
		Case 2
			; Get ShortDate format
			$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x1F, "str", "", "long", 255)
			If Not @error And $lngX[0] <> 0 Then
				$sTempDate = $lngX[3]
			Else
				$sTempDate = "M/d/yyyy"
			EndIf
		Case 3
			;
			; Get Time format
			If $asTimePart[0] > 1 Then
				$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x1003, "str", "", "long", 255)
				If Not @error And $lngX[0] <> 0 Then
					$sTempTime = $lngX[3]
				Else
					$sTempTime = "h:mm:ss tt"
				EndIf
			EndIf
		Case 4
			If $asTimePart[0] > 1 Then
				$sTempTime = "hh:mm"
			EndIf
		Case 5
			If $asTimePart[0] > 1 Then
				$sTempTime = "hh:mm:ss"
			EndIf
	EndSwitch
	; Format DATE
	If $sTempDate <> "" Then
		;   Const $LOCALE_SDATE = 0x1D            ;  date separator
		$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x1D, "str", "", "long", 255)
		If Not @error And $lngX[0] <> 0 Then
			$sTempTime = StringReplace($sTempTime, "/", $lngX[3])
		EndIf
		$iWday = _DateToDayOfWeek($asDatePart[1], $asDatePart[2], $asDatePart[3])
		$asDatePart[3] = StringRight("0" & $asDatePart[3], 2) ; make sure the length is 2
		$asDatePart[2] = StringRight("0" & $asDatePart[2], 2) ; make sure the length is 2
		$sTempDate = StringReplace($sTempDate, "d", "@")
		$sTempDate = StringReplace($sTempDate, "m", "#")
		$sTempDate = StringReplace($sTempDate, "y", "&")
		$sTempDate = StringReplace($sTempDate, "@@@@", _DateDayOfWeek($iWday, 0))
		$sTempDate = StringReplace($sTempDate, "@@@", _DateDayOfWeek($iWday, 1))
		$sTempDate = StringReplace($sTempDate, "@@", $asDatePart[3])
		$sTempDate = StringReplace($sTempDate, "@", StringReplace(StringLeft($asDatePart[3], 1), "0", "") & StringRight($asDatePart[3], 1))
		$sTempDate = StringReplace($sTempDate, "####", _DateMonthOfYear($asDatePart[2], 0))
		$sTempDate = StringReplace($sTempDate, "###", _DateMonthOfYear($asDatePart[2], 1))
		$sTempDate = StringReplace($sTempDate, "##", $asDatePart[2])
		$sTempDate = StringReplace($sTempDate, "#", StringReplace(StringLeft($asDatePart[2], 1), "0", "") & StringRight($asDatePart[2], 1))
		$sTempDate = StringReplace($sTempDate, "&&&&", $asDatePart[1])
		$sTempDate = StringReplace($sTempDate, "&&", StringRight($asDatePart[1], 2))
	EndIf
	; Format TIME
	If $sTempTime <> "" Then
		$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x28, "str", "", "long", 255)
		If Not @error And $lngX[0] <> 0 Then
			$sAM = $lngX[3]
		Else
			$sAM = "AM"
		EndIf
		$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x29, "str", "", "long", 255)
		If Not @error And $lngX[0] <> 0 Then
			$sPM = $lngX[3]
		Else
			$sPM = "PM"
		EndIf
		;   Const $LOCALE_STIME = 0x1E            ;  time separator
		$lngX = DllCall("kernel32.dll", "long", "GetLocaleInfo", "long", 0x400, "long", 0x1E, "str", "", "long", 255)
		If Not @error And $lngX[0] <> 0 Then
			$sTempTime = StringReplace($sTempTime, ":", $lngX[3])
		EndIf
		If StringInStr($sTempTime, "tt") Then
			If $asTimePart[1] < 12 Then
				$sTempTime = StringReplace($sTempTime, "tt", $sAM)
				If $asTimePart[1] = 0 Then $asTimePart[1] = 12
			Else
				$sTempTime = StringReplace($sTempTime, "tt", $sPM)
				If $asTimePart[1] > 12 Then $asTimePart[1] = $asTimePart[1] - 12
			EndIf
		EndIf
		$asTimePart[1] = StringRight("0" & $asTimePart[1], 2) ; make sure the length is 2
		$asTimePart[2] = StringRight("0" & $asTimePart[2], 2) ; make sure the length is 2
		$asTimePart[3] = StringRight("0" & $asTimePart[3], 2) ; make sure the length is 2
		$sTempTime = StringReplace($sTempTime, "hh", StringFormat("%02d", $asTimePart[1]))
		$sTempTime = StringReplace($sTempTime, "h", StringReplace(StringLeft($asTimePart[1], 1), "0", "") & StringRight($asTimePart[1], 1))
		$sTempTime = StringReplace($sTempTime, "mm", StringFormat("%02d", $asTimePart[2]))
		$sTempTime = StringReplace($sTempTime, "ss", StringFormat("%02d", $asTimePart[3]))
		$sTempDate = StringStripWS($sTempDate & " " & $sTempTime, 3)
	EndIf
	Return ($sTempDate)
EndFunc   ;==>_DateTimeFormat

;===============================================================================
;
; Description:      Returns the the julian date in format YYDDD
; Parameter(s):     $iJulianDate    - Julian date number
;                   $Year  - Year in format YYYY
;                   $Month - Month in format MM
;                   $sDate - Day of the month format DD
; Requirement(s):   None
; Return Value(s):  On Success - Returns the Date in the parameter vars
;                   On Failure - 0  and sets @ERROR = 1
; Author(s):        Jeremy Landes / Jos van der Zande
; Note(s):          None
;
;===============================================================================
Func _DateJulianDayNo($iYear, $iMonth, $iDay)
	Local $sFullDate
	Local $aiDaysInMonth
	Local $iJDay
	Local $iCntr
	; Verify If InputDate is valid
	$sFullDate = StringFormat("%04d/%02d/%02d", $iYear, $iMonth, $iDay)
	If Not _DateIsValid($sFullDate) Then
		SetError(1)
		Return ""
	EndIf
	; Build JDay value
	$iJDay = 0
	$aiDaysInMonth = __DaysInMonth($iYear)
	For $iCntr = 1 To $iMonth - 1
		$iJDay = $iJDay + $aiDaysInMonth[$iCntr]
	Next
	$iJDay = ($iYear * 1000) + ($iJDay + $iDay)
	Return $iJDay
EndFunc   ;==>_DateJulianDayNo

;===============================================================================
;
; Description:      Returns the date for a julian date in format YYDDD
; Parameter(s):     $iJDate  - Julian date number
; Requirement(s):   None
; Return Value(s):  On Success - Returns the Date in format YYYY/MM/DD
;                   On Failure - 0  and sets @ERROR = 1
; Author(s):        Jeremy Landes / Jos van der Zande
; Note(s):          None
;
;===============================================================================
Func _JulianToDate($iJDay)
	Local $aiDaysInMonth
	Local $iYear
	Local $iMonth
	Local $iDay
	Local $iDays
	Local $iMaxDays
	Local $sSep = "/"
	; Verify If InputDate is valid
	$iYear = Int($iJDay / 1000)
	$iDays = Mod($iJDay, 1000)
	$iMaxDays = 365
	If _DateIsLeapYear($iYear) Then $iMaxDays = 366
	If $iDays > $iMaxDays Then
		SetError(1)
		Return ""
	EndIf
	; Convert to regular date
	$aiDaysInMonth = __DaysInMonth($iYear)
	$iMonth = 1
	While $iDays > $aiDaysInMonth[ $iMonth ]
		$iDays = $iDays - $aiDaysInMonth[ $iMonth ]
		$iMonth = $iMonth + 1
	WEnd
	$iDay = $iDays
	Return StringFormat("%04d%s%02d%s%02d", $iYear, $sSep, $iMonth, $sSep, $iDay)
EndFunc   ;==>_JulianToDate

;===============================================================================
;
; Description:      Returns the current Date and Time in the pc's format
; Parameter(s):     None
; Requirement(s):   None
; Return Value(s):  On Success - Date in pc's format
; Author(s):        Jos van der Zande
; Note(s):          None
;
;===============================================================================
Func _Now()
	Return (_DateTimeFormat(@YEAR & "/" & @MON & "/" & @MDAY & " " & @HOUR & ":" & @MIN & ":" & @SEC, 0))
EndFunc   ;==>_Now

;===============================================================================
;
; Description:      Returns the current Date and Time in format YYYY/MM/DD HH:MM:SS
; Parameter(s):     None
; Requirement(s):   None
; Return Value(s):  On Success - Date in in format YYYY/MM/DD HH:MM:SS
; Author(s):        Jos van der Zande
; Note(s):          None
;
;===============================================================================
Func _NowCalc()
	Return (@YEAR & "/" & @MON & "/" & @MDAY & " " & @HOUR & ":" & @MIN & ":" & @SEC)
EndFunc   ;==>_NowCalc
;===============================================================================
;
; Description:      Returns the current Date in format YYYY/MM/DD
; Parameter(s):     None
; Requirement(s):   None
; Return Value(s):  On Success - Date in in format YYYY/MM/DD
; Author(s):        Jos van der Zande
; Note(s):          None
;
;===============================================================================
Func _NowCalcDate()
	Return (@YEAR & "/" & @MON & "/" & @MDAY)
EndFunc   ;==>_NowCalcDate

;===============================================================================
;
; Description:      Returns the current Date in the pc's format
; Parameter(s):     None
; Requirement(s):   None
; Return Value(s):  On Success - Date in pc's format
; Author(s):        Jos van der Zande (Larry's idea)
; Note(s):          None
;
;===============================================================================
Func _NowDate()
	Return (_DateTimeFormat(@YEAR & "/" & @MON & "/" & @MDAY, 0))
EndFunc   ;==>_NowDate

;===============================================================================
;
; Description:      Returns the current Date and Time in the pc's format
; Parameter(s):     None
; Requirement(s):   None
; Return Value(s):  On Success - Date in pc's format
; Author(s):        Jos van der Zande
; Note(s):          None
;
;===============================================================================
Func _NowTime($sType = 3)
	If $sType < 3 Or $sType > 5 Then $sType = 3
	Return (_DateTimeFormat(@YEAR & "/" & @MON & "/" & @MDAY & " " & @HOUR & ":" & @MIN & ":" & @SEC, $sType))
EndFunc   ;==>_NowTime

;===============================================================================
;
; Description:      Sets the local date of the system / computer
; Parameter(s):     $iDay 	- Day of month Values: 1-31
;                   $iMonth - Moth Values: 1-12
;                   $iYear (optional)  - Year Values: > 0 (windows might restrict this further!!)
;
; Requirement(s):   DllCall
; Return Value(s):  On Success - 1
;                   On Failure - 0 sets @ERROR = 1 and @EXTENDED (Windows API error code)
;
; Error code(s): 	http://msdn.microsoft.com/library/default.asp?url=/library/en-us/debug/base/system_error_codes.asp
;
; Author(s):        /dev/null
; Note(s):          -
;
;===============================================================================
Func _SetDate($iDay, $iMonth = 0, $iYear = 0)

	Local $iRetval, $SYSTEMTIME, $lpSystemTime

	;============================================================================
	;== Some error checking
	;============================================================================
	If $iYear = 0 Then $iYear = @YEAR
	If $iMonth = 0 Then $iMonth = @MON
	If Not _DateIsValid($iYear & "/" & $iMonth & "/" & $iDay) Then Return 1

	$SYSTEMTIME = DllStructCreate("ushort;ushort;ushort;ushort;ushort;ushort;ushort;ushort")
	$lpSystemTime = DllStructGetPtr($SYSTEMTIME)

	;============================================================================
	;== Get the local system time to fill up the SYSTEMTIME structure
	;============================================================================
	$iRetval = DllCall("kernel32.dll", "long", "GetLocalTime", "ptr", $lpSystemTime)

	;============================================================================
	;== Change the necessary values
	;============================================================================
	DllStructSetData($SYSTEMTIME, 4, $iDay)
	If $iMonth > 0 Then DllStructSetData($SYSTEMTIME, 2, $iMonth)
	If $iYear > 0 Then DllStructSetData($SYSTEMTIME, 1, $iYear)

	;============================================================================
	;== Set the new date
	;============================================================================
	$iRetval = DllCall("kernel32.dll", "long", "SetLocalTime", "ptr", $lpSystemTime)

	;============================================================================
	;== If DllCall was successfull, check for an error of the API Call
	;============================================================================
	If @error = 0 Then
		If $iRetval[0] = 0 Then
			Local $lastError = DllCall("kernel32.dll", "int", "GetLastError")
			SetExtended($lastError[0])
			SetError(1)
			Return 0
		Else
			Return 1
		EndIf
		;============================================================================
		;== If DllCall was UNsuccessfull, return an error
		;============================================================================
	Else
		SetError(1)
		Return 0
	EndIf

EndFunc   ;==>_SetDate

;===============================================================================
;
; Description:      Sets the local time of the system / computer
; Parameter(s):     $iHour 	- hour Values: 0-23
;                   $iMinute - minute Values: 0-59
;                   $iSecond (optional)  - second Values: 0-59
;
; Requirement(s):   DllCall
; Return Value(s):  On Success - 1
;                   On Failure - 0 sets @ERROR = 1 and @EXTENDED (Windows API error code)
;
; Error code(s): 	http://msdn.microsoft.com/library/default.asp?url=/library/en-us/debug/base/system_error_codes.asp
;
; Author(s):        /dev/null
; Note(s):          -
;
;===============================================================================
Func _SetTime($iHour, $iMinute, $iSecond = 0)

	Local $iRetval, $SYSTEMTIME, $lpSystemTime

	;============================================================================
	;== Some error checking
	;============================================================================
	If $iHour < 0 Or $iHour > 23 Then Return 1
	If $iMinute < 0 Or $iMinute > 59 Then Return 1
	If $iSecond < 0 Or $iSecond > 59 Then Return 1

	$SYSTEMTIME = DllStructCreate("ushort;ushort;ushort;ushort;ushort;ushort;ushort;ushort")
	$lpSystemTime = DllStructGetPtr($SYSTEMTIME)

	;============================================================================
	;== Get the local system time to fill up the SYSTEMTIME structure
	;============================================================================
	$iRetval = DllCall("kernel32.dll", "long", "GetLocalTime", "ptr", $lpSystemTime)

	;============================================================================
	;== Change the necessary values
	;============================================================================
	DllStructSetData($SYSTEMTIME, 5, $iHour)
	DllStructSetData($SYSTEMTIME, 6, $iMinute)
	If $iSecond > 0 Then DllStructSetData($SYSTEMTIME, 7, $iSecond)

	;============================================================================
	;== Set the new time
	;============================================================================
	$iRetval = DllCall("kernel32.dll", "long", "SetLocalTime", "ptr", $lpSystemTime)

	;============================================================================
	;== If DllCall was successfull, check for an error of the API Call
	;============================================================================
	If @error = 0 Then
		If $iRetval[0] = 0 Then
			Local $lastError = DllCall("kernel32.dll", "int", "GetLastError")
			SetExtended($lastError[0])
			SetError(1)
			Return 0
		Else
			Return 1
		EndIf
		;============================================================================
		;== If DllCall was UNsuccessfull, return an error
		;============================================================================
	Else
		SetError(1)
		Return 0
	EndIf

EndFunc   ;==>_SetTime

;===============================================================================
;
; Description:      Converts the specified tick amount to hours, minutes, and
;                   seconds.
; Parameter(s):     $iTicks - Tick amount
;                   $iHours - Variable to store the hours (ByRef)
;                   $iMins  - Variable to store the minutes (ByRef)
;                   $iSecs  - Variable to store the seconds (ByRef)
; Requirement(s):   None
; Return Value(s):  On Success - 1
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Marc <mrd at gmx de>
; Note(s):          None
;
;===============================================================================
Func _TicksToTime($iTicks, ByRef $iHours, ByRef $iMins, ByRef $iSecs)
	If Number($iTicks) > 0 Then
		$iTicks = Round($iTicks / 1000)
		$iHours = Int($iTicks / 3600)
		$iTicks = Mod($iTicks, 3600)
		$iMins = Int($iTicks / 60)
		$iSecs = Round(Mod($iTicks, 60))
		; If $iHours = 0 then $iHours = 24
		Return 1
	ElseIf Number($iTicks) = 0 Then
		$iHours = 0
		$iTicks = 0
		$iMins = 0
		$iSecs = 0
		Return 1
	Else
		SetError(1)
		Return 0
	EndIf
EndFunc   ;==>_TicksToTime

;===============================================================================
;
; Description:      Converts the specified hours, minutes, and seconds to ticks.
; Parameter(s):     $iHours - Hours
;                   $iMins  - Minutes
;                   $iSecs  - Seconds
; Requirement(s):   None
; Return Value(s):  On Success - Converted tick amount
;                   On Failure - 0 and sets @ERROR = 1
; Author(s):        Marc <mrd at gmx de>
;                   SlimShady: added the default time and made parameters optional
; Note(s):          None
;
;===============================================================================
Func _TimeToTicks($iHours = @HOUR, $iMins = @MIN, $iSecs = @SEC)
	;==============================================
	; Local Constant/Variable Declaration Section
	;==============================================
	Local $iTicks

	If StringIsInt($iHours) And StringIsInt($iMins) And StringIsInt($iSecs) Then
		$iTicks = 1000 * ((3600 * $iHours) + (60 * $iMins) + $iSecs)
		Return $iTicks
	Else
		SetError(1)
		Return 0
	EndIf
EndFunc   ;==>_TimeToTicks

;===============================================================================
;
; Function Name:    _WeekNumberISO()
; Description:      Find out the week number of current date OR date given in parameters
;
; Parameter(s):     $iDay    - Day value (default = current day)
;                   $iMonth    - Month value (default = current month)
;                   $iYear    - Year value (default = current year)
; Requirement(s):
; Return Value(s):  On Success     - Returns week number of given date
;                   On Failure     - returns -1  and sets @ERROR = 1 on faulty parameters values
;                   On non-acceptable weekstart value sets @ERROR = 99 and uses default (Sunday) as starting day
; Author(s):        Tuape
;                   JdeB: modified to UDF standards & Doc.
;                   JdeB: Change calculation logic.
;===============================================================================
;
Func _WeekNumberISO($iYear = @YEAR, $iMonth = @MON, $iDay = @MDAY)
	Local $idow, $iDow0101

	; Check for erroneous input in $Day, $Month & $Year
	If $iDay > 31 Or $iDay < 1 Then
		SetError(1)
		Return -1
	ElseIf $iMonth > 12 Or $iMonth < 1 Then
		SetError(1)
		Return -1
	ElseIf $iYear < 1 Or $iYear > 2999 Then
		SetError(1)
		Return -1
	EndIf

	$idow = _DateToDayOfWeekISO($iYear, $iMonth, $iDay);
	$iDow0101 = _DateToDayOfWeekISO($iYear, 1, 1);

	If ($iMonth = 1 And 3 < $iDow0101 And $iDow0101 < 7 - ($iDay - 1)) Then
		;days before week 1 of the current year have the same week number as
		;the last day of the last week of the previous year
		$idow = $iDow0101 - 1;
		$iDow0101 = _DateToDayOfWeekISO($iYear - 1, 1, 1);
		$iMonth = 12
		$iDay = 31
		$iYear = $iYear - 1
	ElseIf ($iMonth = 12 And 30 - ($iDay - 1) < _DateToDayOfWeekISO($iYear + 1, 1, 1) And _DateToDayOfWeekISO($iYear + 1, 1, 1) < 4) Then
		; days after the last week of the current year have the same week number as
		; the first day of the next year, (i.e. 1)
		Return 1;
	EndIf

	Return Int((_DateToDayOfWeekISO($iYear, 1, 1) < 4) + 4 * ($iMonth - 1) + (2 * ($iMonth - 1) + ($iDay - 1) + $iDow0101 - $idow + 6) * 36 / 256)

EndFunc   ;==>_WeekNumberISO


;===============================================================================
;
; Function Name:    _WeekNumber()
; Description:      Find out the week number of current date OR date given in parameters
;
; Parameter(s):     $iDay    - Day value (default = current day)
;                   $iMonth    - Month value (default = current month)
;                   $iYear    - Year value (default = current year)
;                   $iWeekstart - Week starts from Sunday (1, default) or Monday (2)
; Requirement(s):
; Return Value(s):  On Success     - Returns week number of given date
;                   On Failure     - returns -1  and sets @ERROR = 1 on faulty parameters values
;                   On non-acceptable weekstart value sets @ERROR = 99 and uses default (Sunday) as starting day
; Author(s):        JdeB
;===============================================================================
;
Func _WeekNumber($iYear = @YEAR, $iMonth = @MON, $iDay = @MDAY, $iWeekStart = 1)
	Local $iDow0101, $iDow0101ny
	Local $iDate, $iStartWeek1, $iEndWeek1, $iEndWeek1Date, $iStartWeek1ny, $iStartWeek1Dateny
	Local $iCurrDateDiff, $iCurrDateDiffny

	; Check for erroneous input in $Day, $Month & $Year
	If $iDay > 31 Or $iDay < 1 Then
		SetError(1)
		Return -1
	ElseIf $iMonth > 12 Or $iMonth < 1 Then
		SetError(1)
		Return -1
	ElseIf $iYear < 1 Or $iYear > 2999 Then
		SetError(1)
		Return -1
	ElseIf $iWeekStart < 1 Or $iWeekStart > 2 Then
		SetError(2)
		Return -1
	EndIf
	;
	;$idow = _DateToDayOfWeekISO($iYear, $iMonth, $iDay);
	$iDow0101 = _DateToDayOfWeekISO($iYear, 1, 1);
	$iDate = $iYear & '/' & $iMonth & '/' & $iDay
	;Calculate the Start and End date of Week 1 this year
	If $iWeekStart = 1 Then
		If $iDow0101 = 6 Then
			$iStartWeek1 = 0
		Else
			$iStartWeek1 = -1 * $iDow0101 - 1
		EndIf
		$iEndWeek1 = $iStartWeek1 + 6
	Else
		$iStartWeek1 = $iDow0101 * - 1
		$iEndWeek1 = $iStartWeek1 + 6
	EndIf
	;$iStartWeek1Date = _DateAdd('d',$iStartWeek1,$iYear & '/01/01')
	$iEndWeek1Date = _DateAdd('d', $iEndWeek1, $iYear & '/01/01')
	;Calculate the Start and End date of Week 1 this Next year
	$iDow0101ny = _DateToDayOfWeekISO($iYear + 1, 1, 1);
	;  1 = start on Sunday / 2 = start on Monday
	If $iWeekStart = 1 Then
		If $iDow0101ny = 6 Then
			$iStartWeek1ny = 0
		Else
			$iStartWeek1ny = -1 * $iDow0101ny - 1
		EndIf
		;$IEndWeek1ny = $iStartWeek1ny + 6
	Else
		$iStartWeek1ny = $iDow0101ny * - 1
		;$IEndWeek1ny = $iStartWeek1ny + 6
	EndIf
	$iStartWeek1Dateny = _DateAdd('d', $iStartWeek1ny, $iYear + 1 & '/01/01')
	;$iEndWeek1Dateny = _DateAdd('d',$IEndWeek1ny,$iYear+1 & '/01/01')
	;number of days after end week 1
	$iCurrDateDiff = _DateDiff('d', $iEndWeek1Date, $iDate) - 1
	;number of days before next week 1 start
	$iCurrDateDiffny = _DateDiff('d', $iStartWeek1Dateny, $iDate)
	;
	; Check for end of year
	If $iCurrDateDiff >= 0 And $iCurrDateDiffny < 0 Then Return 2 + Int($iCurrDateDiff / 7)
	; > week 1
	If $iCurrDateDiff < 0 Or $iCurrDateDiffny >= 0 Then Return 1
EndFunc   ;==>_WeekNumber


;===============================================================================
;
; Description:      returns an Array that contains the numbers of days per month
;                   te specified year
; Parameter(s):     $iYear
; Requirement(s):   None
; Return Value(s):  On Success - Array that contains the numbers of days per month
;                   On Failure - none
; Author(s):        Jos van der Zande / Jeremy Landes
; Note(s):          None
;
;===============================================================================
Func __DaysInMonth($iYear)
	Local $aiDays
	$aiDays = StringSplit("31,28,31,30,31,30,31,31,30,31,30,31", ",")
	If _DateIsLeapYear($iYear) Then $aiDays[2] = 29
	Return $aiDays
EndFunc   ;==>__DaysInMonth

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Program Files\AutoIt3\beta\Include\Date.au3>
; ----------------------------------------------------------------------------


TrayTip("[ Tantra Bot System] " , "Author: booster101" & @CRLF, 8 , 16)

Opt ("SendKeyDelay", 50       )
Opt ("WinTitleMatchMode", 4  )
Opt ("SendKeyDownDelay", 50  )
Opt ("MouseClickDownDelay", 50)

HotKeySet ("!1", "GetPosUpperLeft")
HotKeySet ("!2", "GetPosLowerLeft")
HotKeySet ("!3", "GetPosUpperRight")
HotKeySet ("!4", "GetPosLowerRight")
HotkeySet("{F5}", "GetPos")
HotKeySet ("{F8}",  "Auto_5"     )
HotKeySet ("{F9}",  "TradeSpam" ) ; Spam Trade
HotkeySet ("{F10}", "Stop"       )
HotKeySet ("{F11}", "Left_Click2")
HotKeySet ("+!m", "MenuLaunch")  ;Shift-Alt-m
HotKeySet ("+!a", "AutoLoginMenu")  ;Shift-Alt-a
HotKeySet ("+!t", "MandaraTeleportHack")  ;Shift-Alt-a

$versionnumber = "1.05"
Global $defaultstatus = "Please visit http://www.bigbytes.net"
Global $status
Global $wintitle
Global $inputAddress
Global $statuslabel

$lootflag = 1 ;default loot On
$MonsterSelectTargetFlag = 1 ;default on
$count = 0
$count2 = 0
dim $b
dim $EXIT
$Value = "";


$Process = 'HTLauncher.exe' ;-> Target process
$PID = ProcessExists($Process) ;-> Get Process ID

$baseAddressCharacter = 0x109BE1D0 ;-> myCharacter baseaddress
$myMaxHPoffset = 268;-> myCharacterMaxHP offset H10C
$myCurHPoffset = 272;-> myCharacterCurHP offset H110
$myXaddrOffset = 9178;--> my cur X location offset &H23DA
$myYaddrOffset = 9182;--> my cur X location offset &H23DE
$TPMaxAddrOffset = 804 ;baseaddr + &H324 (Hex 324 = Dec 804)
$TPCurAddrOffset = 808;baseaddr + &H328
$baseAddressTargetMonster = 0x109BE280 ;-> Read/write address
$chatMsgAddrbase = 0x109BE200 ; -> chat message base address
$chatMsgAddrbaseOffset = 2848 ; -> &HB20

$chatMsgAddrbase2FirstLine = 58708 ; ->  &HE554
$chatMsgAddrbase2SecondLine = 59240 ; -> &HE768
$chatMsgAddrbase2ThirdLine = 59772 ; -> &HE97c
$chatMsgAddrbase2LastLine = 114036 ; -> &H1bd74
$searchbreak = "is about to break"

$charactername = 0x109BE0E0 ;-> character name address
$htlusernameaddress = 0x109FF290 ;-> username in htl
$minimumdamagerate = 0x10A03278 ;

$baseAddressDialog = 0x109BE23C ;-> dialog baseaddress
$invitedPartyOffset = 667 ; baseAddressDialog + 29B

$baseAddressDialogTrade = 0x109BE248 ;-> dialog baseaddress
$TradeOffset = 270 ; baseAddressDialog + 10E

_writeValue(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset), 49)
$dialogTradeRequest = _GetDialogData(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset))

$baseAddressDialogDuel = 0x10A03DF0 ;-> dialog baseaddress
_writeValue($baseAddressDialogDuel, 49)
$dialogDuelRequest = _GetDialogData($baseAddressDialogDuel)


$maxHP = _GetData($baseAddressCharacter,$myMaxHPoffset)

_writeValue(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset), 49)
$dialogParty = _GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset))

$botmenu = GUICreate("Tantra Bot System! Version "&$versionnumber,400,430)

$statuslabel = GUICtrlCreateLabel ($defaultstatus,0,392,400,300,BitOr($SS_SIMPLE,$SS_SUNKEN))
$filemenu = GUICtrlCreateMenu ("&File")
$fileitem = GUICtrlCreateMenuitem ("Open",$filemenu)
GUICtrlSetState(-1,$GUI_DEFBUTTON)
$windowmenu = GUICtrlCreateMenu ("Menu")
$menumain = GUICtrlCreateMenuitem ("Main (ALT+SHIFT+M)",$windowmenu)
$menuteleport = GUICtrlCreateMenuitem ("Portal Hack (ALT+SHIFT+T)",$windowmenu)
$menuautologin = GUICtrlCreateMenuitem ("AutoLogin (ALT+SHIFT+A)",$windowmenu)
$viewstatusitem = GUICtrlCreateMenuitem ("Statusbar",$windowmenu)
GUICtrlSetState($viewstatusitem,$GUI_CHECKED)
$helpmenu = GUICtrlCreateMenu ("?")
$saveitem = GUICtrlCreateMenuitem ("Save",$filemenu)
;$readme = GUICtrlCreateMenuitem ("ReadMe",$helpmenu)
$infoabout = GUICtrlCreateMenuitem ("About",$helpmenu)
$exititem = GUICtrlCreateMenuitem ("Exit",$filemenu)
;$recentfilesmenu = GUICtrlCreateMenu ("Recent Files",$filemenu,1)
$separator1 = GUICtrlCreateMenuitem ("",$filemenu,2)



$MonsterCombo = GUICtrlCreateEdit ("ANTI AVARA CARA"& @CRLF, 10,70,120,50,$ES_AUTOVSCROLL+$WS_VSCROLL)
GUICtrlSetData ($MonsterCombo, "Kemsa Tonyo"&@CRLF&"Mosa Tonyo"&@CRLF,1)




$checkReplyPM = GUICtrlCreateCheckbox ("Reply PM", 140, 10, 70, 20)
GUICtrlSetState(-1, 4)
$checkReplyText = GUICtrlCreateInput ("Pil0t p0h", 220,10,170,20)

$canceltraderequest = GUICtrlCreateCheckbox ("Cancel Trade/Duel", 140, 50, 110, 20)
GUICtrlSetState(-1, 1)
;GUICtrlSetState($canceltraderequest, $GUI_DISABLE)
$canceltraderequestreply = GUICtrlCreateInput ("Sorry am busy", 250,50,170,20)

$checkboxautologin = GUICtrlCreateCheckbox ("Auto-Login", 140, 70, 80, 20)
GUICtrlSetState(-1, 4)

$autologindelay = GUICtrlCreateInput ("0", 220, 70, 60, 20)
GUICtrlSetState(-1, 4)

$autologindelaylabel = GUICtrlCreateLabel ("Delay b4 login (msec)", 280, 70, 140, 20)
GUICtrlSetState(-1, 4)


$antistuckup = GUICtrlCreateCheckbox ("Turn/Move when stuck", 140, 90, 130, 20)
GUICtrlSetState(-1, 1)

$autosilf = GUICtrlCreateCheckbox ("Auto Silfrijan", 300, 90, 130, 20)
GUICtrlSetState($autosilf , 4)
GUICtrlSetTip($autosilf,"Self-resu option")
$autosilfxcoord = GUICtrlCreateInput ("150", 320,110,40,18)
$autosilfycoord = GUICtrlCreateInput ("500", 320,130,40,18)
GUICtrlCreateLabel ("X",  300, 110, 20)
GUICtrlCreateLabel ("Y",  300, 130, 20)
GUICtrlSetState($autosilfxcoord, $GUI_DISABLE)
GUICtrlSetState($autosilfycoord, $GUI_DISABLE)


GUICtrlCreateLabel ("Spin in msec",  140, 110, 70)
GUICtrlCreateLabel ("Move in msec",  140, 130, 70)
$antistuckspin = GUICtrlCreateInput ("150", 210,110,40,18)
$antistuckmove = GUICtrlCreateInput ("500", 210,130,40,18)



$partyOK = GUICtrlCreateCheckbox ("Join party", 140, 30, 80, 20)
GUICtrlSetState(-1, 1)
$partyjoinedreply = GUICtrlCreateInput ("Thanks", 220,30,80,20)
GUICtrlSetState($partyjoinedreply , $GUI_ENABLE)
$partydeniedreply = GUICtrlCreateInput ("Sorry solo mode.", 300,30,90,20)
GUICtrlSetState($partydeniedreply , $GUI_DISABLE)


$checkLoot = GUICtrlCreateCheckbox ("Loot On/Off", 10, 10, 80, 20)
GUICtrlSetState(-1, 1)
$lootduration = GUICtrlCreateInput ("200", 90,10,30,18)
GUICtrlSetTip($lootduration,"Loot duration in msec")

$checkMonsterSelectTarget = GUICtrlCreateCheckbox ("Enable monster select", 10, 30, 120, 20)
GUICtrlSetState(-1, $MonsterSelectTargetFlag)
$ClearMonsterList = GUICtrlCreateButton("Clear",70,50,60,20)  ;Talk like GM enable/disable button
$AddMonsterList = GUICtrlCreateButton("Add",10,50,50,20)  ;Talk like GM enable/disable button

$StartBot = GUICtrlCreateButton("Start",10,370,120,20)  ;Talk like GM enable/disable button
GUICtrlSetState(-1,$GUI_ENABLE)

$EXITMAIN = GUICtrlCreateButton("Exit",270,370,120,20)  ;This causes the application to exit

;GUICtrlCreateLabel ("www.bigbytes.net",  150, 370, 100)

$pausecheckbox = GUICtrlCreateCheckBox ("CHECK TO PAUSE BOT",  130, 370, 135)
GUICtrlSetState(-1,4)
;GUICtrlCreateLabel ("Hotkeys: ALT+SHIFT+M = SHOW THIS MENU",  10, 330, 400)
;GUICtrlCreateLabel ("Hotkeys: F8 = RUN, F9 = Spam Trade, F10 = Pause, F11 = Left Clicker ",  10, 350, 400)
$wintitle = GUICtrlCreateInput("Tantra Launcher",100, 345, 100)
GUICtrlCreateLabel ("Window Caption",  10, 345, 80)
;$closetantrabutton = GUICtrlCreateButton("Tantra Launcher",100, 345, 100)

GUICtrlCreateLabel ("Skill Function",  10, 160, 80)	; first cell 50 width

GUICtrlCreateLabel ("Cool Time (msec)",  100, 160, 100)	; first cell 50 width
$F1Skill1 = GUICtrlCreateCheckbox ("F1 Skill 1", 10, 180, 60, 20)
GUICtrlSetState(-1, $lootflag)
$F1Skill1Delay = GUICtrlCreateInput ("200", 110,180,60,20)

$F1Skill2 = GUICtrlCreateCheckbox ("F1 Skill 2", 10, 200, 60, 20)
GUICtrlSetState(-1, $lootflag)
$F1Skill2Delay = GUICtrlCreateInput ("200", 110,200,60,20)

$F1Skill3 = GUICtrlCreateCheckbox ("F1 Skill 3", 10, 220, 60, 20)
GUICtrlSetState(-1, $lootflag)
$F1Skill3Delay = GUICtrlCreateInput ("200", 110,220,60,20)

$F1Skill4 = GUICtrlCreateCheckbox ("F1 Skill 4", 10, 240, 60, 20)
GUICtrlSetState(-1, $lootflag)
$F1Skill4Delay = GUICtrlCreateInput ("200", 110,240,60,20)

$F1Skill7 = GUICtrlCreateCheckbox ("F1 YudaHeal 7", 10, 260, 90, 20)
GUICtrlSetState(-1, $lootflag)
GUICtrlSetTip($F1Skill7,"Yuda specific option, disable if you are not yuda")
$F1Skill7Delay = GUICtrlCreateInput ("2000", 110,260,40,20)
$F1Skill7percent = GUICtrlCreateInput ("70", 155,260,30,20)
GUICtrlSetTip($F1Skill7percent,"Execute heal if below this value, loop until hp is above this value.")
GUICtrlCreateLabel (" %",  185, 260, 30)	; first cell 50 width


$F1Skill8 = GUICtrlCreateCheckbox ("F1 HP Pots 8", 10, 280, 90, 20)
GUICtrlSetState(-1, 4)
$F1Skill8Delay = GUICtrlCreateInput ("2000", 110,280,40,20)
$F1Skill8percent = GUICtrlCreateInput ("70", 155,280,30,20)
GUICtrlCreateLabel (" %",  185, 280, 30)	; first cell 50 width

$F1Skill9 = GUICtrlCreateCheckbox ("F1 TP Pots 9", 10, 300, 90, 20)
GUICtrlSetState(-1, 4)
$F1Skill9Delay = GUICtrlCreateInput ("2000", 110,300,40,20)
$F1Skill9percent = GUICtrlCreateInput ("70", 155,300,30,20)
GUICtrlCreateLabel (" %",  185, 300, 30)	; first cell 50 width

$DelayAfterFindTargetCheckBox = GUICtrlCreateCheckbox ("Delay B4 Skill", 10, 320, 90, 20)
GUICtrlSetState(-1, 1)
$DelayAfterFindTargetDelay = GUICtrlCreateInput ("1000", 110,320,40,20)


GUICtrlCreateLabel ("Buff Function",  210, 160, 80)	; first cell 50 width
GUICtrlCreateLabel ("Duration (msec)",  300, 160, 100)	; first cell 50 width

$F2Skill1 = GUICtrlCreateCheckbox ("F2 Buff 1", 210, 180, 60, 20)
GUICtrlSetState(-1, $lootflag)
$F2Skill1Delay = GUICtrlCreateInput ("480000", 310,180,60,20)

$F2Skill2 = GUICtrlCreateCheckbox ("F2 Buff 2", 210, 200, 60, 20)
GUICtrlSetState(-1, $lootflag)
$F2Skill2Delay = GUICtrlCreateInput ("600000", 310,200,60,20)

$F2Skill3 = GUICtrlCreateCheckbox ("F2 Buff 3", 210, 220, 60, 20)
GUICtrlSetState(-1, $lootflag)
$F2Skill3Delay = GUICtrlCreateInput ("300000", 310,220,60,20)

$F2Skill4 = GUICtrlCreateCheckbox ("F2 Buff 4", 210, 240, 60, 20)
GUICtrlSetState(-1, 4)
$F2Skill4Delay = GUICtrlCreateInput ("300000", 310,240,60,20)

$F2Skill6 = GUICtrlCreateCheckbox ("F2 TradeSpam 6", 210, 260, 95, 20)
GUICtrlSetState(-1, 4)
$F2Skill6Delay = GUICtrlCreateInput ("2000", 310,260,60,20)

$F2Skill7 = GUICtrlCreateCheckbox ("F2 YudaHeal 7", 210, 280, 90, 20)
GUICtrlSetState(-1, $lootflag)
$F2Skill7Delay = GUICtrlCreateInput ("2000", 310,280,60,20)

$F2Skill9 = GUICtrlCreateCheckbox ("F2 Repair 9", 210, 300, 80, 20)
GUICtrlSetState(-1, $lootflag)
$F2Skill9Delay = GUICtrlCreateInput ("2000", 310,300,60,20)

$buff1flag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
$buff2flag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
$buff3flag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
$buff4flag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
$buffrepairflag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())

$checkSkill = GUICtrlCreateCheckbox ("Skill On/Off", 10, 140, 80, 20)
GUICtrlSetState(-1, 1)
$checkSkillR = GUICtrlCreateCheckbox ("R", 90, 140, 30, 20)
GUICtrlSetState(-1, 1)
GUICtrlSetTip($checkSkillR, "Uncheck this option for Mage")
$checkRepair = GUICtrlCreateCheckbox ("Auto Repair", 10, 120, 120, 20)
GUICtrlSetState(-1, 4)
GUICtrlSetState($F2Skill9Delay, $GUI_DISABLE)
GUICtrlSetState($F2Skill9, $GUI_DISABLE)
GUICtrlSetState($F2Skill9, 4)


$autologinmenu = GUICreate("Auto-Login: booster101")  ; will create a dialog box that when displayed is centered
$currentdir = @WorkingDir

$filemenu2 = GUICtrlCreateMenu ("&File")
$fileitem2 = GUICtrlCreateMenuitem ("Open",$filemenu2)
GUICtrlSetState(-1,$GUI_DEFBUTTON)
$windowmenu2 = GUICtrlCreateMenu ("Menu")
$menumain2 = GUICtrlCreateMenuitem ("Main (ALT+SHIFT+M)",$windowmenu2)
$menuteleport2 = GUICtrlCreateMenuitem ("Portal Hack (ALT+SHIFT+T)",$windowmenu2)
$menuautologin2 = GUICtrlCreateMenuitem ("AutoLogin (ALT+SHIFT+A)",$windowmenu2)
$helpmenu2 = GUICtrlCreateMenu ("?")
$saveitem2 = GUICtrlCreateMenuitem ("Save",$filemenu2)
$infoabout2 = GUICtrlCreateMenuitem ("About",$helpmenu2)
$exititem2 = GUICtrlCreateMenuitem ("Exit",$filemenu2)
$separator2 = GUICtrlCreateMenuitem ("",$filemenu2,2)


$username=GUICtrlCreateInput ("", 70,10,100,20)
GUICtrlCreateLabel ("Username", 10, 10, 60, 20)

$password=GUICtrlCreateInput ("", 250,10,100,20,$ES_PASSWORD)
GUICtrlCreateLabel ("Password", 180, 10, 60, 20)

$characterselect = GUICtrlCreateCombo ("Character1", 70,35,100) ; create first item
GUICtrlSetData(-1,"Character2|Character3","Character1")
GUICtrlCreateLabel ("Char Select", 10, 35, 60, 20)

$serverselect = GUICtrlCreateCombo ("Manas", 250,35,100) ; create first item
GUICtrlSetData(-1,"Diyana|Kriya|Samadi|Warzone","Diyana")
GUICtrlCreateLabel ("Server Select", 180, 35, 70, 20)

$delay1=GUICtrlCreateInput ("3000", 10,60,50,20)
GUICtrlCreateLabel ("Wait for Tantra", 70, 60, 100, 20)

$delay2=GUICtrlCreateInput ("25000", 10,90,50,20)
GUICtrlCreateLabel ("Wait for login", 70, 90, 100, 20)

$delay3=GUICtrlCreateInput ("10000", 10,120,50,20)
GUICtrlCreateLabel ("Delay after login", 70, 120, 100, 20)

$delay4=GUICtrlCreateInput ("45000", 200,60,50,20)
GUICtrlCreateLabel ("Delay after server select", 260, 60, 150, 20)

$delay5=GUICtrlCreateInput ("5000", 200,90,50,20)
GUICtrlCreateLabel ("Delay after char select", 260, 90, 150, 20)

$delay6=GUICtrlCreateInput ("5000", 200,120,50,20)
GUICtrlCreateLabel ("Delay for Quiz", 260, 120, 100, 20)

;$myXcoord=GUICtrlCreateInput ("50", 280,310,30,20)
;$myYcoord=GUICtrlCreateInput ("50", 370,310,30,20)



$question=GUICtrlCreateInput ("Quiz question here.", 10,150,370,20)

$answerupperleft=GUICtrlCreateInput ("Choice1", 10,180,100,20)
GUICtrlCreateLabel ("X", 110, 180, 20, 20)
GUICtrlCreateLabel ("Y", 150, 180, 20, 20)
$upperleftX=GUICtrlCreateInput ("186", 120,180,30,20)
$upperleftY=GUICtrlCreateInput ("316", 160,180,30,20)
GUICtrlCreateLabel ("ALT+1 FOR X/Y", 10, 200, 200, 20)

$answerupperright=GUICtrlCreateInput ("Choice2", 200,180,100,20)
GUICtrlCreateLabel ("X", 300, 180, 20, 20)
GUICtrlCreateLabel ("Y", 340, 180, 20, 20)
$upperrightX=GUICtrlCreateInput ("383", 310,180,30,20)
$upperrightY=GUICtrlCreateInput ("317", 350,180,30,20)
GUICtrlCreateLabel ("ALT+3 FOR X/Y", 200, 200, 200, 20)

$answerlowerleft=GUICtrlCreateInput ("Choice3", 10,240,100,20)
GUICtrlCreateLabel ("X", 110, 240, 20, 20)
GUICtrlCreateLabel ("Y", 150, 240, 20, 20)
$lowerleftX=GUICtrlCreateInput ("191", 120,240,30,20)
$lowerleftY=GUICtrlCreateInput ("342", 160,240,30,20)
GUICtrlCreateLabel ("ALT+2 FOR X/Y", 10, 260, 200, 20)

$answerlowerright=GUICtrlCreateInput ("Choice4", 200,240,100,20)
GUICtrlCreateLabel ("X", 300, 240, 20, 20)
GUICtrlCreateLabel ("Y", 340, 240, 20, 20)
$lowerrightX=GUICtrlCreateInput ("380", 310,240,30,20)
$lowerrightY=GUICtrlCreateInput ("341", 350,240,30,20)
GUICtrlCreateLabel ("ALT+4 FOR X/Y", 200, 260, 200, 20)

$LOGIN = GUICtrlCreateButton("LogIn Once",150,350,120,20)
;$AUTOLOGIN = GUICtrlCreateButton("Auto-LogIn",150,320,120,20)
$BACK = GUICtrlCreateButton("Bot Menu",280,350,120,20)  ;This causes the application to exit
$DIALOG = GUICtrlCreateButton("Ans Quiz",10,350,120,20)

GUICtrlCreateLabel("Hotkey to show this Menu: ALT+SHIFT+A",10,320,250,20)

;$FILERUN = GUICtrlCreateButton("File",10,330,120,20)

$tantradir=GUICtrlCreateInput ("C:\Program Files\Tantra\", 10,290,300,20)

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))

$questionAddress = 0x10A069BC
$choiceupperleftAddress = 0x10A06DBC
$choicelowerleftAddress = 0x10A06DE4
$choiceupperrightAddress = 0x10A06DD0
$choicelowerrightAddress = 0x10A06DF8


$teleportgui = GUICreate("Mandara Teleport Hack: booster101")  ; will create a dialog box that when displayed is centered


$filemenu3 = GUICtrlCreateMenu ("&File")
$fileitem3 = GUICtrlCreateMenuitem ("Open",$filemenu3)
GUICtrlSetState(-1,$GUI_DEFBUTTON)
$windowmenu3 = GUICtrlCreateMenu ("Menu")
$menumain3 = GUICtrlCreateMenuitem ("Main (ALT+SHIFT+M)",$windowmenu3)
$menuteleport3 = GUICtrlCreateMenuitem ("Portal Hack (ALT+SHIFT+T)",$windowmenu3)
$menuautologin3 = GUICtrlCreateMenuitem ("AutoLogin (ALT+SHIFT+A)",$windowmenu3)
$helpmenu3 = GUICtrlCreateMenu ("?")
$saveitem3 = GUICtrlCreateMenuitem ("Save",$filemenu3)
$infoabout3 = GUICtrlCreateMenuitem ("About",$helpmenu3)
$exititem3 = GUICtrlCreateMenuitem ("Exit",$filemenu3)
;$recentfilesmenu = GUICtrlCreateMenu ("Recent Files",$filemenu3,1)
$separator3 = GUICtrlCreateMenuitem ("",$filemenu3,2)



$APP1_ComboBOX = GUICtrlCreateCombo ("Mandara Village", 10,180) ; create first item
GUICtrlSetData(-1,"Mandara Portal|Entrance to Mara Land|Way to Coliseum|Entrance to Shambala|Anu Village|Shambala Castle|Holy Army's Barracks|Town of Ruins|Town of Ruins Westside Forest(1000)|Horseman Town Well|Horseman Dungeon 1st(600)|Horseman Dungeon 1st end|Horseman Dungeon 2nd(800)|Shambala Dungeon|Shambala Dungeon 1st(600)|Shambala Dungeon 1st end|Shambala Dungeon 2st(1000)|Shambala Castle|Jina Village Portal|Rest Place(400)|Country Borderline(400)|Small Sea Port|Pamir Plains(400)|Mantra Temple|Village of the Outcast|Flower Field(400)|Temple of Fire(400)|Brahma Castle|Vishnu Castle|Shiva Castle|Brahman Front Line|Brahman Right Flank|Brahman Left Flank|Vishnite Front Line|Vishnite Right Flank|Vishnite Left Flank|Shivan Front Line|Shivan Right Flank|Shivan Left Flank|Brahman Chaturanga|SHivan Chaturanga|Vishnite Chaturanga|Emperor's Tomb Entrance|[Not Yet Implemented]|West Tomb(20000)|East Tomb(20000)|Mahi Durga|Ahb Durga|Danva Durga|Nar Durga|Mahi Durga Gate|Ahb Durga Gate|Danva Durga Gate|Nar Durga Gate|Mahi Durga Stronghold|Ahb Durga Stronghold|Danva Durga Stronghold|Nar Durga Stronghold|Mahi Conference Hall|Ahb Conference Hall|Danva Conference Hall|Nar Conference Hall|Mahi Kaustbam|Ahb Kaustbam|Danva Kaustbam|Nar Kaustbam|Town Biryu|Biryu Commercial Area|Biryu Downtown Area|[Not Yet Implemented]|Mudha Dungeon[x]","Mandara Village") ; add other item snd set a new default


$RUN = GUICtrlCreateButton("Hack Portal",10,350,120,20)  ;This in the Run button
$RUN2 = GUICtrlCreateButton("Search Address",140,350,120,20)  ;Talk like GM enable/disable button
$BACK2 = GUICtrlCreateButton("Bot Menu",270,350,120,20)  ;This causes the application to exit


GUICtrlCreateLabel ("How to use Mandara Teleport hack.",  10, 20, 300)
GUICtrlCreateLabel ("Click Search Address button to update Target Address.",  10, 50, 300)
GUICtrlCreateLabel ("Wait till the search is finished.",  10, 65, 300)
GUICtrlCreateLabel ("Go to Mandara portal.",  10, 80, 300)
GUICtrlCreateLabel ("Go to Select destination.",  10, 95, 300)
GUICtrlCreateLabel ("Then click Hack Portal button.",  10, 110, 300)
GUICtrlCreateLabel ("Click the portal then click on your target destination.",  10, 125, 300)
GUICtrlCreateLabel ("Note: Update Target Address value ONCE per Tantra session.",  10, 125, 300)


;$messageBox = GUICtrlCreateCheckbox ("Enable MsgBox per cycle", 10, 160, 150, 20)

$bytes2read = GUICtrlCreateInput ("1000000", 10,210,120,20)
GUICtrlCreateLabel("Number of bytes per Read",130, 210, 130)

$searchStart = GUICtrlCreateInput ("4194304", 10,230,130,20)
GUICtrlCreateLabel("Start Address",130, 230, 130)

$searchEnd = GUICtrlCreateInput ("2147483647", 10,250,120,20)
GUICtrlCreateLabel("End Address",130, 250, 130)

$searchBytes = GUICtrlCreateInput ("6800000069000000A1000000A2000000A3", 10,270,230,20)
GUICtrlCreateLabel("Array of bytes to search",240, 270, 130)
$currentaddress = GUICtrlCreateLabel ("current search address",  10, 290, 200)

$searchlen = StringLen(GUICtrlRead($searchBytes))/2

$inputAddress = GUICtrlCreateInput ("9B8EAC4", 10,320,120,20)
GUICtrlCreateLabel("Target Address",130, 320, 80)



GUISwitch($botmenu)

Call ("MenuLaunch")

;********** Trade Spam **********

Func TradeSpam ()

Msgbox(0,"Trade Spam","To adjust delay to compensate for ingame lag, use F2 TradeSpam 6 duration input box")

While (1)

Send ("{F2}6 ")
Sleep (Number(GUICtrlRead($F2Skill6Delay)))
MouseClick ("Left")
Call ("Unfocus")
MouseUp ("Left")
Sleep (Number(GUICtrlRead($F2Skill6Delay)))
;Send (" ", 1)
Send ("{ENTER} ")


WEnd
EndFunc

;********** Left Clicker **********

Func Left_Click2 ()

Msgbox(0,"Left Clicker","To adjust click speed, use F2 TradeSpam 6 duration input box")

While (1)

MouseClick ("Left")
Call ("Unfocus")
MouseUp ("Left")
Sleep (Number(GUICtrlRead($F2Skill6Delay)))




WEnd
EndFunc


;********** Auto 5 F8**********

Func Auto_5 ()

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))

If Not WinExists(GUICtrlRead($wintitle)) And Not ProcessExists("HTLauncher.exe") Then
	If GUICtrlRead($checkboxautologin) = 4 then
	   MsgBox(0, "Tantra Process", "Run the Tantra client first before pressing F8 Or use the auto-login feature.")
	   Send("+!m")
	EndIf
Else

	_writeValue(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset), 49)
	$dialogTradeRequest = _GetDialogData(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset))
	$maxHP = _GetData($baseAddressCharacter,$myMaxHPoffset)
	_writeValue(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset), 49)
	$dialogParty = _GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset))
	_writeValue($baseAddressDialogDuel, 49)
        $dialogDuelRequest = _GetDialogData($baseAddressDialogDuel)
        ;MsgBox(0,"Info","username in htl is "&_readText2($htlusernameaddress))
EndIf

While (1)

checkifdisconnected()

If WinExists(GUICtrlRead($wintitle)) then
        Call ("check4Repair")
	Call ("AutoSilf")
	Call("_replyToPM")
	$myDamage = _GetData($baseAddressCharacter,$myMaxHPoffset - 4)
	Call ("buff1")
	Call ("buff2")
	Call ("buff3")
	Call ("buff4")
	Call ("CheckPartyDialog")
	Call ("CheckTradeRequestDialog")
	Call ("CheckDuelRequestDialog")
	Call("_replyToPM")
	Call ("FindTarget2")
	Call ("skill1")
	Call ("loot1")
	Call("_replyToPM")
	Call ("FindTarget2")
	Call ("skill2")
	Call ("CheckPartyDialog")
	Call ("CheckTradeRequestDialog")
	Call ("CheckDuelRequestDialog")
	antistuck($myDamage)
	Call ("loot1")
	Call("_replyToPM")
	Call ("FindTarget2")
	Call ("skill4")
	Call ("loot1")
	Call("_replyToPM")
	Call ("FindTarget2")
	Call ("skill3")
	antistuck($myDamage)
	Call ("loot1")
	Call("_replyToPM")
	Call ("FindTarget2")
	Call ("CheckPartyDialog")
	Call ("CheckTradeRequestDialog")
	Call ("CheckDuelRequestDialog")
	$count2 = $count2 + 1
EndIf

WEnd

EndFunc


;********** Stop **********

; Stops The Program.

Func Stop ()

;$handle = WinGetHandle(GUICtrlRead($wintitle))

WinActivate("Tantra Bot System!")
$count = 0
$count2 = 0
GUICtrlSetData($statuslabel,"Paused....")
Send("+!m")

While 1 = 1
	$count = 0
	$count2 = 0
	Sleep (1000)
Wend

EndFunc


;********** Repeater **********

; Continuous Loop Of Program.

While (1)
Sleep (1)
WEnd


;********** Functions: Delay **********

; Delay Interval In Between Skills.

Func Delay_1 ()

Sleep (1000)
EndFunc

Func Delay_2 ()
Sleep (2000)
EndFunc

Func Delay_3 ()
ControlSend($handle, "", "","{r down}")
Sleep (300)
ControlSend($handle, "", "","{r up}")
EndFunc

Func Delay_4 ()

Sleep (400)
EndFunc

Func Delay ()
 Sleep (1000)
EndFunc

;********** Functions: Skill **********

Func skill1 ()
  CheckIfPause()
  GetCharacterStatus()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F1Skill1) = 1 then
	Call ("TP_Pots")
	ControlSend($handle, "", "","{1 down}")
	Sleep (Number(GUICtrlRead($F1Skill1Delay)))
	ControlSend($handle, "", "","{1 up}")
     Else
       If GuICtrlRead($checkSkillR) = 1 then
	 ControlSend($handle, "", "","r ")
       EndIf
     Endif
  Else
       If GuICtrlRead($checkSkillR) = 1 then
	ControlSend($handle, "", "","r ")
       EndIf
  EndIf
EndFunc

Func skill2 ()
  CheckIfPause()
  GetCharacterStatus()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F1Skill2) = 1 then
	Call ("TP_Pots")
	ControlSend($handle, "", "","{2 down}")
	Sleep (Number(GUICtrlRead($F1Skill2Delay)))
	ControlSend($handle, "", "","{2 up}")
     Else
       If GuICtrlRead($checkSkillR) = 1 then
	 ControlSend($handle, "", "","r ")
       EndIf
     Endif
  Else
       If GuICtrlRead($checkSkillR) = 1 then
	 ControlSend($handle, "", "","r ")
       EndIf
  EndIf
EndFunc

Func skill3 ()
  CheckIfPause()
  GetCharacterStatus()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F1Skill3) = 1 then
	Call ("TP_Pots")
	ControlSend($handle, "", "","{3 down}")
	Sleep (Number(GUICtrlRead($F1Skill3Delay)))
	ControlSend($handle, "", "","{3 up}")
     Else
       If GuICtrlRead($checkSkillR) = 1 then
	 ControlSend($handle, "", "","r ")
       EndIf
     Endif
  Else
       If GuICtrlRead($checkSkillR) = 1 then
	 ControlSend($handle, "", "","r ")
       EndIf
  EndIf

EndFunc

Func skill4 ()
  CheckIfPause()
  GetCharacterStatus()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F1Skill4) = 1 then
	Call ("TP_Pots")
	ControlSend($handle, "", "","{4 down}")
	Sleep (Number(GUICtrlRead($F1Skill4Delay)))
	ControlSend($handle, "", "","{4 up}")
     Else
       If GuICtrlRead($checkSkillR) = 1 then
	 ControlSend($handle, "", "","r ")
       EndIf
     Endif
  Else
       If GuICtrlRead($checkSkillR) = 1 then
	 ControlSend($handle, "", "","r ")
       EndIf
  EndIf
EndFunc

Func loot1 ()
   CheckIfPause()
   GetCharacterStatus()
   If GUICtrlRead($checkLoot) = 1 then
      ControlSend($handle, "", "","{f down}")
      Sleep (Number(GUICtrlRead($lootduration)))
      ControlSend($handle, "", "","{f up}")
   EndIf
EndFunc

;********** Functions: A1 **********

Func A1 ()

ControlSend($handle, "", "","1 ")
Sleep (50)
EndFunc

;********** Functions: A2 **********

Func A2 ()

ControlSend($handle, "", "","1 ")
Sleep (50)
EndFunc


;********** Functions: FindTarget2 **********

Func FindTarget2 ()
CheckIfPause()
$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
GetCharacterStatus()

If $MonsterSelectTargetFlag = 1 then

$flag = 0

Do
   $curHP = _GetData($baseAddressCharacter,$myCurHPoffset)
if $curHP <> 0 then
   ControlSend($handle, "", "","te ")
   Sleep (500)
   $t = StringUpper(_GetMonsterName($baseAddressTargetMonster))
   If  StringInStr(StringUpper(GUICtrlRead($MonsterCombo,1)),$t) >= 1   then
	$flag = 1
	If GUICtrlRead($checkSkillR) = 1 then
	    ControlSend($handle, "", "","r ")
	EndIf
	If GUICtrlRead($DelayAfterFindTargetCheckBox) = 1 then
	   Sleep(Number(GUICtrlRead($DelayAfterFindTargetDelay)))
	EndIf
   Else
	If GUICtrlRead($checkSkillR) = 1 Or GUICtrlRead($checkSkill) = 1 then
	   Call ("Click_Name")
	   ControlSend($handle, "", "","{a down}")
	   Sleep (Number(GUICtrlRead($antistuckspin)))
	   ControlSend($handle, "", "","{a up}")
	   ControlSend($handle, "", "","{w down}")
	   Sleep (Number(GUICtrlRead($antistuckmove)))
	   ControlSend($handle, "", "","{w up}")
        EndIf
   EndIf
Else
   $flag = 1
   Call ("AutoSilf")
EndIf
   ;MsgBox(0,"Info", "flag is "&$flag&" target is "&$t)

Until $flag = 1

Else
   If GUICtrlRead($checkSkillR) = 1 then
      ControlSend($handle, "", "","ter ")
   ElseIf GUICtrlRead($checkSkill) = 1 then
      ControlSend($handle, "", "","te ")
   EndIf
   Sleep (500)
EndIf

If GUICtrlRead($checkSkillR) = 1 Or GUICtrlRead($checkSkill) = 1  then
 Sleep (5)
 Call ("Heal_30")
 Call ("Heal_Pots")
EndIf

EndFunc

;********** Functions: Anti-stuckup **********

Func antistuck ($myDamage)
CheckIfPause()
$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))

   If GUICtrlRead($antistuckup) = 1 then
	$myDamage2 = _GetData($baseAddressCharacter,$myMaxHPoffset - 4)
        ;MsgBox(0,"Info", "myDamage = "&String($myDamage)& @CRLF &"myDamage2 = "&String($myDamage2))
        If $myDamage = $myDamage2 then
		Call ("Click_Name")
		ControlSend($handle, "", "","{a down}")
		Sleep (Number(GUICtrlRead($antistuckspin)))
		ControlSend($handle, "", "","{a up}")
		ControlSend($handle, "", "","{w down}")
		Sleep (Number(GUICtrlRead($antistuckmove)))
		ControlSend($handle, "", "","{w up}")
	EndIf
    EndIf
EndFunc


;********** Functions: Buff **********

Func buff1 ()

  CheckIfPause()
  GetCharacterStatus()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F2Skill1) = 1 then
       If ((_DateDiff( 's',"1970/01/01 00:00:00",_NowCalc()) - $buff1flag) * 1000) >= Number(GUICtrlRead($F2Skill1Delay)) Or $count2 = 0 then
	If Not $count2 = 0 Then
		Call ("FindTarget2")
		Sleep (2000)
	EndIf
	ControlSend($handle, "", "","{F2} ")
	Sleep(100)
	ControlSend($handle, "", "","1 ")
	Sleep(100)
	$buff1flag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
	Sleep (2000)
	ControlSend($handle, "", "","{F1} ")
       EndIf
     Endif
  EndIf
EndFunc

Func buff2 ()
  CheckIfPause()
  GetCharacterStatus()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F2Skill2) = 1 then
       If ((_DateDiff( 's',"1970/01/01 00:00:00",_NowCalc()) - $buff2flag) * 1000) >= Number(GUICtrlRead($F2Skill2Delay)) Or $count2 = 0 then
	If Not $count2 = 0 Then
		Call ("FindTarget2")
	EndIf
	Sleep (2000)
	ControlSend($handle, "", "","{F2} ")
	Sleep(100)
	ControlSend($handle, "", "","2 ")
	Sleep(100)
	$buff2flag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
	Sleep (2000)
	ControlSend($handle, "", "","{F1} ")
       EndIf
     Endif
  EndIf
EndFunc

Func buff3 ()
  CheckIfPause()
  GetCharacterStatus()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F2Skill3) = 1 then
       If ((_DateDiff( 's',"1970/01/01 00:00:00",_NowCalc()) - $buff3flag) * 1000) >= Number(GUICtrlRead($F2Skill3Delay)) Or $count2 = 0 then
	If Not $count2 = 0 Then
		Call ("FindTarget2")
	EndIf
	ControlSend($handle, "", "","{F2} ")
	Sleep (2000)
	ControlSend($handle, "", "","3 ")
	Sleep(100)
	$buff3flag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
	Sleep (2000)
	ControlSend($handle, "", "","{F1} ")
	Sleep(50)
       EndIf
     Endif
  EndIf
EndFunc

Func buff4 ()
  CheckIfPause()
  GetCharacterStatus()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F2Skill4) = 1 then
       If ((_DateDiff( 's',"1970/01/01 00:00:00",_NowCalc()) - $buff4flag) * 1000) >= Number(GUICtrlRead($F2Skill4Delay)) Or $count2 = 0 then
	If Not $count2 = 0 Then
		Call ("FindTarget2")
	EndIf
	ControlSend($handle, "", "","{F2} ")
	Sleep (2000)
	ControlSend($handle, "", "","4 ")
	Sleep (2000)
	$buff4flag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
	Sleep(25)
	ControlSend($handle, "", "","{F1} ")
       EndIf
     Endif
  EndIf
EndFunc

;********** Functions: Auto Repair **********

Func check4Repair()
  CheckIfPause()
  If GUICtrlRead($checkSkill) = 1 then
     If GUICtrlRead($F2Skill9) = 1 then
       If _GetValue($minimumdamagerate) < Number(GUICtrlRead($F2Skill9Delay)) then
	ControlSend($handle, "", "","{F2} ")
	Call ("FindTarget2")
	Sleep (2000)
	ControlSend($handle, "", "","9 ")
	$buffrepairflag = _DateDiff( 's',"1970/01/01 00:00:00",_NowCalc())
	Sleep(25)
	ControlSend($handle, "", "","{F1} ")
       EndIf
       ;Msgbox(0,"Info",String(_GetValue($minimumdamagerate)));
     Endif
  EndIf
EndFunc


Func Click_Name () ; ensures command are not sent to chat window

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
ClickInControl("left",GUICtrlRead($wintitle),"","",520,430)

EndFunc

Func Click_Dialog ()

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
ClickInControl("left",GUICtrlRead($wintitle),"","",400,400)

EndFunc


Func Heal_30()

If GUICtrlRead($F1Skill7) = 1 then

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))

Do
   $curHP = _GetData($baseAddressCharacter,$myCurHPoffset)
   $maxHP = _GetData($baseAddressCharacter,$myMaxHPoffset)
   $a = $curHP / $maxHP
   ;MsgBox(0,"Info", "curHP = "&String($curHP)& @CRLF &"$maxHP = "&String($maxHP))
   If $a <= (Number(GUICtrlRead($F1Skill7percent)) / 100) And ($curHP <> 0) then
	ControlSend($handle, "", "","7 ")
	Sleep (100)
	Call ("findtarget2")
	If GUICtrlRead($F1Skill8) = 1 then
   		$curHP = _GetData($baseAddressCharacter,$myCurHPoffset)
   		$maxHP = _GetData($baseAddressCharacter,$myMaxHPoffset)
		$a = $curHP / $maxHP
		If $a <= (Number(GUICtrlRead($F1Skill8percent)) / 100) then
			ControlSend($handle, "", "","{F1}")
			Sleep (10)
			ControlSend($handle, "", "","8 ")
			Sleep (Number(GUICtrlRead($F1Skill8Delay)))
		EndIf
	EndIf
	Sleep (Number(GUICtrlRead($F1Skill7Delay)))
   ElseIf $curHP = 0 then
        Call ("AutoSilf")
   EndIf
Until $a >= (Number(GUICtrlRead($F1Skill7percent)) / 100) Or $curHP = 0

EndIf

EndFunc

Func Heal_Pots()

If GUICtrlRead($F1Skill8) = 1 then
$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
$hpflag = 0
Do
   $curHP = _GetData($baseAddressCharacter,$myCurHPoffset)
   $maxHP = _GetData($baseAddressCharacter,$myMaxHPoffset)
   $a = $curHP / $maxHP
   ;MsgBox(0,"Info", "curHP = "&String($curHP)& @CRLF &"$maxHP = "&String($maxHP))

If $curHP = 0 then
	$hpflag = 1
	Call ("AutoSilf")
        ;MsgBox(0,"Info", "autosilf")
Else
   If $a <= (Number(GUICtrlRead($F1Skill8percent)) / 100) then
	;MsgBox(0,"Info", "curHP = "&String($curHP)& @CRLF &"$maxHP = "&String($maxHP))
	ControlSend($handle, "", "","{F1}")
	Sleep (10)
	ControlSend($handle, "", "","8 ")
	Sleep (Number(GUICtrlRead($F1Skill8Delay)))
	If $curHP = _GetData($baseAddressCharacter,$myCurHPoffset) Then
		;MsgBox(0,"Info", "hpflag set to 1")
		$hpflag = 1
		GUICtrlSetState($F1Skill8, 4)
	Endif
   EndIf

EndIf

Until $a >= (Number(GUICtrlRead($F1Skill8percent)) / 100) Or $curHP = 0 Or $hpflag = 1

EndIf

EndFunc

Func TP_Pots()

If GUICtrlRead($F1Skill9) = 1 then

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
$tpflag = 0
Do
   $curHP = _GetData($baseAddressCharacter,$myCurHPoffset)
   $TPCur = _GetData($baseAddressTargetMonster,$TPCurAddrOffset)
   $TPMax = _GetData($baseAddressTargetMonster,$TPMaxAddrOffset)
   $a = $TPCur / $TPMax
   ;MsgBox(0,"Info", "curHP = "&String($curHP)& @CRLF &"$maxHP = "&String($maxHP))
   If $a <= (Number(GUICtrlRead($F1Skill9percent)) / 100) then
	If $curHP <> 0 then
		ControlSend($handle, "", "","{F1}")
		Sleep (10)
		ControlSend($handle, "", "","9 ")
		Sleep (Number(GUICtrlRead($F1Skill9Delay)))
		If $TPCur = _GetData($baseAddressTargetMonster,$TPCurAddrOffset) Then
			$tpflag = 1
			GUICtrlSetState($F1Skill9, 4)
		Endif
	ElseIf $curHP = 0 then
		$tpflag = 1
		Call ("AutoSilf")
	EndIf
   EndIf
Until ($a >= (Number(GUICtrlRead($F1Skill9percent)) / 100)) Or $curHP = 0 Or $tpflag = 1

EndIf

EndFunc

Func _GetData($Address,$myOffset)

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
   $a = _GetValue($Address)
   $a = $a + $myOffset
   $myHP = _GetValue($a)
   Return $myHP

EndFunc

Func _GetAddressOffset($Address,$myOffset)
$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))


   $a = _GetValue($Address)
   $a = $a + $myOffset
   Return $a

EndFunc


Func _GetValue($Address)
$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
;Msgbox(0,"info","handle = "&$handle & @CRLF & "PID = " & $PID)
    $OpenProcess = _MemOpen(0x38, False, $PID) ;-> Enable reading/writing to the process and get the handle

    $byte1 = _MemRead($OpenProcess, $Address, 1) ;-> Read a 1 byte value from the defined address
    $Address = $Address + 1
    $byte2 = _MemRead($OpenProcess, $Address, 1) ;-> Read a 1 byte value from the defined address
    $Address = $Address + 1
    $byte3 = _MemRead($OpenProcess, $Address, 1) ;-> Read a 1 byte value from the defined address
    $Address = $Address + 1
    $byte4 = _MemRead($OpenProcess, $Address, 1) ;-> Read a 1 byte value from the defined address


    $Value = Hex($byte4, 2)&Hex($byte3, 2)&Hex($byte2, 2)&Hex($byte1, 2)
    $myInt = Dec($Value)


    _MemClose($OpenProcess) ;-> Disable reading/writing to the process

    Return $myInt

EndFunc

Func _writeValue($Address, $value1)
$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
    $OpenProcess = _MemOpen(0x38, False, $PID) ;-> Enable reading/writing to the process and get the handle

    $byte1 = _MemWrite($OpenProcess, $Address, $Value1, 1) ;-> Read a 1 byte value from the defined address
    ;$Value = Hex($byte4, 2)&Hex($byte3, 2)&Hex($byte2, 2)&Hex($byte1, 2)
    ;$myInt = Dec($Value)


    _MemClose($OpenProcess) ;-> Disable reading/writing to the process

    ;Return $myInt

EndFunc


Func _GetMonsterName($baseAddressTargetMonster)
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
   $Address = _GetValue($baseAddressTargetMonster)
   $text = _readText($Address)
   Return $text

EndFunc

Func _GetDialogData($Address)
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
   $text = _readText($Address)
   Return $text

EndFunc

Func _GetTextData($Address)
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
   $text = _readText2($Address)
   Return $text

EndFunc


Func _readText($Address)
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
   $b = ""
   Do
	$a = _GetAscii($Address);
	$Address1 = $Address + 1
	$a1 = _GetAscii($Address1);
        ;MsgBox(0,"Info", "a is "&Hex($a,2)&" b is "&Hex($b,2))
	If Chr($a) = "[" or (Hex($a,2) = "00" And Hex($a1,2) = "00") then
	   $b =$b
	Else
           $b = $b & Chr($a)
	EndIf
	$Address = $Address + 1
	;MsgBox(0,"Info", "a is "&$a&" a1 is "&$a1)

   Until Chr($a) = "[" or (Hex($a,2) = "00" And Hex($a1,2) = "00")
   ;Until Chr($a) = "["

   Return $b

EndFunc

Func _readText2($Address)
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
   $b = ""
   Do
	$a = _GetAscii($Address);
	$Address1 = $Address + 1
	$a1 = _GetAscii($Address1);
        ;MsgBox(0,"Info", "a is "&Hex($a,2)&" b is "&Hex($b,2))
	If Not (Hex($a,2) = "00" And Hex($a1,2) = "00") then
           $b = $b & Chr($a)
	EndIf
	$Address = $Address + 1
	;MsgBox(0,"Info", "a is "&$a&" a1 is "&$a1)

   Until (Hex($a,2) = "00" And Hex($a1,2) = "00")


   Return $b

EndFunc




Func _GetAscii($baseAddressCharacter)

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
    $Address = $baseAddressCharacter
    $OpenProcess = _MemOpen(0x38, False, $PID) ;-> Enable reading/writing to the process and get the handle

    $byte1 = _MemRead($OpenProcess, $Address, 1) ;-> Read a 1 byte value from the defined address
    _MemClose($OpenProcess) ;-> Disable reading/writing to the process
    Return $byte1

EndFunc

Func CheckPartyDialog()
   CheckIfPause()
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
	If GUICtrlRead($partyOK) = 1 then
 		If $dialogParty <> _GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset)) then
			Call("Click_Dialog")
			ControlSend($handle, "", "","{ENTER}")
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			Sleep (500)
			ControlSend($handle, "", "","/g "&GUICtrlRead($partyjoinedreply))
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			_writeValue(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset), 49)
                        $dialogParty = _GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset))
		EndIf
	Else
 		If $dialogParty <> _GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset)) then
			Call("Click_Dialog")
			ClickInControl("left",GUICtrlRead($wintitle),"","",600,420)
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			Sleep (500)
			ControlSend($handle, "", "","/s "&GUICtrlRead($partydeniedreply))
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			_writeValue(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset), 49)
                        $dialogParty = _GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset))
		EndIf

	EndIf

EndFunc

Func CheckTradeRequestDialog()
   CheckIfPause()
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
	If GUICtrlRead($canceltraderequest) = 1 then
 		If $dialogTradeRequest <> _GetDialogData(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset)) then
			Call("Click_Dialog")
			ClickInControl("left",GUICtrlRead($wintitle),"","",600,420)
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			Sleep (500)
			ControlSend($handle, "", "","/s "&GUICtrlRead($canceltraderequestreply))
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			_writeValue(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset), 49)
                        $dialogTradeRequest = _GetDialogData(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset))
		EndIf
	EndIf
EndFunc

Func CheckDuelRequestDialog()
   CheckIfPause()
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
	If GUICtrlRead($canceltraderequest) = 1 then
 		If $dialogDuelRequest <> _GetDialogData($baseAddressDialogDuel) then
			Call("Click_Dialog")
			ClickInControl("left",GUICtrlRead($wintitle),"","",600,420) ;599/424
			Sleep (500)
			Call("Click_Dialog")
			ControlSend($handle, "", "","{ENTER}")
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			Sleep (500)
			ControlSend($handle, "", "","/s "&GUICtrlRead($canceltraderequestreply))
			Sleep (500)
			ControlSend($handle, "", "","{ENTER}")
			_writeValue($baseAddressDialogDuel, 49)
                        $dialogDuelRequest = _GetDialogData($baseAddressDialogDuel)
		EndIf
	EndIf
EndFunc

Func _replyToPM ()

CheckIfPause()

   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
;Msgbox(0,"info","test")
;Msgbox(0,"info2",_GetTextData(_GetAddressOffset($chatMsgAddrbase,$chatMsgAddrbaseOffset)))
;Msgbox(0,"info",GUICtrlRead($checkReplyText))
;Msgbox(0,"compare result",String(StringInStr(_GetTextData(_GetAddressOffset($chatMsgAddrbase,$chatMsgAddrbaseOffset)), "From")))
If GUICtrlRead($checkReplyPM) = 1 then
If StringInStr(_GetTextData(_GetAddressOffset($chatMsgAddrbase,$chatMsgAddrbaseOffset)), "From") >= 1 then
     Call ("Click_Name")
     ControlSend($handle, "", "","v ")
     Sleep (50)
     ;Msgbox(0,"info",GUICtrlRead($checkReplyText))
     ControlSend($handle, "", "",GUICtrlRead($checkReplyText))
     Sleep (500)
     ControlSend($handle, "", "","{ENTER}")
     Call ("Click_Name")
;Else
;     Msgbox(0,"info","From not found")
EndIf
EndIf
EndFunc

Func AutoSilf()

CheckIfPause()

If GUICtrlRead($autosilf) = 1 then
   $handle = WinGetHandle(GUICtrlRead($wintitle))
   $PID = WinGetProcess(GUICtrlRead($wintitle))
	$curHP = _GetData($baseAddressCharacter,$myCurHPoffset)
	If $curHP = 0 then
		ClickInControl("left",GUICtrlRead($wintitle),"","",Number(GUICtrlRead($autosilfxcoord)),Number(GUICtrlRead($autosilfycoord)))
		Sleep (500)
		ClickInControl("left",GUICtrlRead($wintitle),"","",515,370)
		Sleep (500)
		Call ("Heal_30")
		Call ("Heal_Pots")
		Call ("TP_Pots")
	EndIf
EndIf

EndFunc

Func GetPos()
    $pos = MouseGetPos()
    GUIctrlSetData($autosilfxcoord,$pos[0])
    GUIctrlSetData($autosilfycoord,$pos[1])
EndFunc

Func MenuLaunch()

GUISwitch($autologinmenu)
GUISetState (@SW_HIDE)
GUISwitch($teleportgui)
GUISetState (@SW_HIDE)
GUISwitch($botmenu)
GUISetState (@SW_SHOW)


Do
        $handle = WinGetHandle(GUICtrlRead($wintitle))
        $PID = WinGetProcess(GUICtrlRead($wintitle))
	$msg = GUIGetMsg()
	If $msg = $viewstatusitem Then
		If BitAnd(GUICtrlRead($viewstatusitem),$GUI_CHECKED) = $GUI_CHECKED Then
			GUICtrlSetState($viewstatusitem,$GUI_UNCHECKED)
			GUICtrlSetState($statuslabel,$GUI_HIDE)
		Else
			GUICtrlSetState($viewstatusitem,$GUI_CHECKED)
			GUICtrlSetState($statuslabel,$GUI_SHOW)
		EndIf
	EndIf

	If $msg = $StartBot then Auto_5()
	If $msg = $infoabout then MyAbout()
	If $msg = $menumain then MenuLaunch()
	If $msg = $saveitem then SaveConfig()
	If $msg = $fileitem then OpenConfig()
	If $msg = $menuteleport then MandaraTeleportHack()
	If $msg = $menuautologin then AutoLoginMenu()
        If $msg = $exititem then Exit
	if $msg = $partyOK then
		If GUICtrlRead($partyOK) = 1 Then
			GUICtrlSetState($partyjoinedreply, $GUI_ENABLE)
			GUICtrlSetState($partydeniedreply, $GUI_DISABLE)
		Else
			GUICtrlSetState($partyjoinedreply, $GUI_DISABLE)
			GUICtrlSetState($partydeniedreply, $GUI_ENABLE)
		EndIf
	endif

	if $msg = $antistuckup then
		If GUICtrlRead($antistuckup) = 1 Then
			GUICtrlSetState($antistuckspin, $GUI_ENABLE)
			GUICtrlSetState($antistuckmove, $GUI_ENABLE)
		Else
			GUICtrlSetState($antistuckspin, $GUI_DISABLE)
			GUICtrlSetState($antistuckmove, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $DelayAfterFindTargetCheckBox then
		If GUICtrlRead($DelayAfterFindTargetCheckBox) = 1 Then
			GUICtrlSetState($DelayAfterFindTargetDelay, $GUI_ENABLE)
		Else
			GUICtrlSetState($DelayAfterFindTargetDelay, $GUI_DISABLE)
		EndIf
	endif


	if $msg = $F1Skill1 then
		If GUICtrlRead($F1Skill1) = 1 Then
			GUICtrlSetState($F1Skill1Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F1Skill1Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $checkboxautologin then
		If GUICtrlRead($checkboxautologin) = 1 Then
			MsgBox(0,"Reminders","Ensure to set your username, password, and proper coordinates.")
			Send("+!a")
		Else

		EndIf
	endif




	if $msg = $F1Skill2 then
		If GUICtrlRead($F1Skill2) = 1 Then
			GUICtrlSetState($F1Skill2Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F1Skill2Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F1Skill3 then
		If GUICtrlRead($F1Skill3) = 1 Then
			GUICtrlSetState($F1Skill3Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F1Skill3Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F1Skill4 then
		If GUICtrlRead($F1Skill4) = 1 Then
			GUICtrlSetState($F1Skill4Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F1Skill4Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F1Skill7 then
		If GUICtrlRead($F1Skill7) = 1 Then
			GUICtrlSetState($F1Skill7Delay, $GUI_ENABLE)
			GUICtrlSetState($F1Skill7percent, $GUI_ENABLE)
		Else
			GUICtrlSetState($F1Skill7Delay, $GUI_DISABLE)
			GUICtrlSetState($F1Skill7percent, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F1Skill8 then
		If GUICtrlRead($F1Skill8) = 1 Then
			GUICtrlSetState($F1Skill8Delay, $GUI_ENABLE)
			GUICtrlSetState($F1Skill8percent, $GUI_ENABLE)
		Else
			GUICtrlSetState($F1Skill8Delay, $GUI_DISABLE)
			GUICtrlSetState($F1Skill8percent, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F1Skill9 then
		If GUICtrlRead($F1Skill9) = 1 Then
			GUICtrlSetState($F1Skill9Delay, $GUI_ENABLE)
			GUICtrlSetState($F1Skill9percent, $GUI_ENABLE)
		Else
			GUICtrlSetState($F1Skill9Delay, $GUI_DISABLE)
			GUICtrlSetState($F1Skill9percent, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F2Skill1 then
		If GUICtrlRead($F2Skill1) = 1 Then
			GUICtrlSetState($F2Skill1Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F2Skill1Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F2Skill2 then
		If GUICtrlRead($F2Skill2) = 1 Then
			GUICtrlSetState($F2Skill2Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F2Skill2Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F2Skill3 then
		If GUICtrlRead($F2Skill3) = 1 Then
			GUICtrlSetState($F2Skill3Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F2Skill3Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F2Skill4 then
		If GUICtrlRead($F2Skill4) = 1 Then
			GUICtrlSetState($F2Skill4Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F2Skill4Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F2Skill7 then
		If GUICtrlRead($F2Skill7) = 1 Then
			GUICtrlSetState($F2Skill7Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F2Skill7Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F2Skill9 then
		If GUICtrlRead($F2Skill9) = 1 Then
			GUICtrlSetState($F2Skill9Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F2Skill9Delay, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $F2Skill6 then
		If GUICtrlRead($F2Skill6) = 1 Then
			GUICtrlSetState($F2Skill6Delay, $GUI_ENABLE)
		Else
			GUICtrlSetState($F2Skill6Delay, $GUI_DISABLE)
		EndIf
	endif

        if $msg = $checkSkill then
		If GUICtrlRead($checkSkill) = 1 Then
			GUICtrlSetState($F1Skill1Delay, $GUI_ENABLE)
			GUICtrlSetState($F1Skill1, 1)
			GUICtrlSetState($F1Skill2Delay, $GUI_ENABLE)
			GUICtrlSetState($F1Skill2, 1)
			GUICtrlSetState($F1Skill3Delay, $GUI_ENABLE)
			GUICtrlSetState($F1Skill3, 1)
			GUICtrlSetState($F1Skill4Delay, $GUI_ENABLE)
			GUICtrlSetState($F1Skill4, 1)

			GUICtrlSetState($F1Skill1, $GUI_ENABLE)
			GUICtrlSetState($F1Skill1, 1)
			GUICtrlSetState($F1Skill2, $GUI_ENABLE)
			GUICtrlSetState($F1Skill2, 1)
			GUICtrlSetState($F1Skill3, $GUI_ENABLE)
			GUICtrlSetState($F1Skill3, 1)
			GUICtrlSetState($F1Skill4, $GUI_ENABLE)
			GUICtrlSetState($F1Skill4, 1)


		Else
			GUICtrlSetState($F1Skill1Delay, $GUI_DISABLE)
			GUICtrlSetState($F1Skill1, 4)
			GUICtrlSetState($F1Skill2Delay, $GUI_DISABLE)
			GUICtrlSetState($F1Skill2, 4)
			GUICtrlSetState($F1Skill3Delay, $GUI_DISABLE)
			GUICtrlSetState($F1Skill3, 4)
			GUICtrlSetState($F1Skill4Delay, $GUI_DISABLE)
			GUICtrlSetState($F1Skill4, 4)


			GUICtrlSetState($F1Skill1, $GUI_DISABLE)
			GUICtrlSetState($F1Skill1, 4)
			GUICtrlSetState($F1Skill2, $GUI_DISABLE)
			GUICtrlSetState($F1Skill2, 4)
			GUICtrlSetState($F1Skill3, $GUI_DISABLE)
			GUICtrlSetState($F1Skill3, 4)
			GUICtrlSetState($F1Skill4, $GUI_DISABLE)
			GUICtrlSetState($F1Skill4, 4)


		EndIf
        endif


	if $msg = $checkReplyPM then
		If GUICtrlRead($checkReplyPM) = 1 Then
			GUICtrlSetState($checkReplyText, $GUI_ENABLE)
		Else
			GUICtrlSetState($checkReplyText, $GUI_DISABLE)
		EndIf
	endif


        if $msg = $checkRepair then
		If GUICtrlRead($checkRepair) = 1 Then
			GUICtrlSetState($F2Skill9Delay, $GUI_ENABLE)
			GUICtrlSetState($F2Skill9, $GUI_ENABLE)
			GUICtrlSetState($F2Skill9, 1)
			Msgbox(0,"Auto Repair Note:","Set hammer to F2 slot 9 then set the MINIMUM DAMAGE RATE accordingly.")
		Else
			GUICtrlSetState($F2Skill9Delay, $GUI_DISABLE)
			GUICtrlSetState($F2Skill9, $GUI_DISABLE)
			GUICtrlSetState($F2Skill9, 4)
		EndIf
        endif
	if $msg = $checkLoot then
		If GUICtrlRead($checkLoot) = 1 Then
			GUICtrlSetState($lootduration, $GUI_ENABLE)
		Else
			GUICtrlSetState($lootduration, $GUI_DISABLE)
		EndIf
	endif
	if $msg = $checkMonsterSelectTarget then
		If GUICtrlRead($checkMonsterSelectTarget) = 1 Then
			$MonsterSelectTargetFlag = 1
			GUICtrlSetState($MonsterCombo, $GUI_ENABLE)
			GUICtrlSetState($ClearMonsterList, $GUI_ENABLE)
			GUICtrlSetState($AddMonsterList, $GUI_ENABLE)
		Else
			$MonsterSelectTargetFlag = 0
			GUICtrlSetState($MonsterCombo, $GUI_DISABLE)
			GUICtrlSetState($ClearMonsterList, $GUI_DISABLE)
			GUICtrlSetState($AddMonsterList, $GUI_DISABLE)
		EndIf
		;msgbox(0,"MONSTER ", GUICtrlRead($checkMonsterSelectTarget))
	endif
	If $msg = $ClearMonsterList then
		GUICtrlSetData($MonsterCombo,"","") ; add other item snd set a new default
	EndIf
	If $msg = $AddMonsterList then
		;msgbox(0,"MONSTER ", GUICtrlRead($MonsterCombo,1))
		;Msgbox(0,"test: ",String(StringInStr(GUICtrlRead($MonsterCombo,1),"Kemsa Tonyo")))

	        ;Msgbox(0, "dialog b4 ",_GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset)))
                ;$v_Read = _writeValue(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset), 49)
     	        ;Msgbox(0, "dialogue data",_GetTextData(_GetAddressOffset($chatMsgAddrbase,$chatMsgAddrbaseOffset)))
     	        ;Msgbox(0, "dialogue data",_GetTextData(_GetAddressOffset($chatMsgAddrbase,$chatMsgAddrbase2FirstLine)))
		;Call ("check4Repair")
     	        ;Msgbox(0, "dialogue data",_GetTextData(_GetAddressOffset($chatMsgAddrbase,$chatMsgAddrbase2FirstLine)))
     	        ;Msgbox(0, "dialogue data",_GetTextData(_GetAddressOffset($chatMsgAddrbase,$chatMsgAddrbase2ThirdLine)))
	        ;Msgbox(0, "dialog after ",_GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset)))
		;Msgbox(0, "dialogue data",_GetTextData(0x16A30410))
                ;Call ("_replyToPM")
		GUICtrlSetData ($MonsterCombo, _GetMonsterName($baseAddressTargetMonster)&@CRLF,1)
	EndIf

	IF $msg = $EXITMAIN then
		Exit
	EndIf

	IF $msg = $GUI_EVENT_CLOSE then
		Exit
	EndIf

	if $msg = $canceltraderequest then
		If GUICtrlRead($canceltraderequest) = 1 Then
			GUICtrlSetState($canceltraderequestreply, $GUI_ENABLE)
		Else
			GUICtrlSetState($canceltraderequestreply, $GUI_DISABLE)
		EndIf
	endif

	if $msg = $autosilf then
		If GUICtrlRead($autosilf) = 1 Then
			GUICtrlSetState($autosilfxcoord, $GUI_ENABLE)
			GUICtrlSetState($autosilfycoord, $GUI_ENABLE)
			Msgbox(0,"How to use auto silfrijan a.k.a. SELF RESU","Set Silfrijan to F1 slot 10 and F2 slot 10, point the mouse to it then press F5 to set the correct X and Y coordinate. Then RUN the bot as usual.")
		Else
			GUICtrlSetState($autosilfxcoord, $GUI_DISABLE)
			GUICtrlSetState($autosilfycoord, $GUI_DISABLE)
		EndIf
	endif


Until $msg = $EXITMAIN Or $msg = $GUI_EVENT_CLOSE

GUISwitch($autologinmenu)
GUISetState (@SW_HIDE)
GUISwitch($botmenu)
GUISetState (@SW_HIDE)


EndFunc

Func AutoLoginMenu()


GUISwitch($botmenu)
GUISetState (@SW_HIDE)
GUISwitch($teleportgui)
GUISetState (@SW_HIDE)

GUISwitch($autologinmenu)
GUISetState (@SW_SHOW)

; Run the GUI until the dialog is closed
While 1

	$msg = GUIGetMsg()
        If $msg = $infoabout2 then MyAbout()
	If $msg = $menumain2 then MenuLaunch()
	If $msg = $saveitem2 then SaveConfig()
	If $msg = $fileitem2 then OpenConfig()
	If $msg = $menuteleport2 then MandaraTeleportHack()
	If $msg = $menuautologin2 then AutoLoginMenu()


	If $msg = $LOGIN then
	    If CheckConnection() = 0 then
		$handle = WinGetHandle(GUICtrlRead($wintitle))
		$PID = WinGetProcess(GUICtrlRead($wintitle))
	        If $PID then
		   ProcessClose($PID)
		   ProcessWaitClose($PID)
                EndIf
		Call ("Login")
	    	Call ("_getDialogQuestion")
	    	_OpenFileDialog()
	    Else
		MsgBox(0,"Info", "Tantra seems to be running already."
	    EndIf
	EndIf

	If $msg = $DIALOG then
	    Call ("_getDialogQuestion")
	    _OpenFileDialog()

	    ;MsgBox(0,"Info", "Value returned is "&CheckInternetConnection())
	EndIf

	IF $msg = $BACK Or $msg = $GUI_EVENT_CLOSE then
                GUISwitch($botmenu)
		Send("+!m")
	EndIf

	If $msg = $GUI_EVENT_CLOSE Then ExitLoop
Wend

EndFunc


Func _getDialogQuestion()

Do

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
ProcessWait($PID)

GUIctrlSetData($question,StringTrimRight(_readText2($questionAddress),1))
GUIctrlSetData($answerupperleft,StringTrimRight(_readText2($choiceupperleftAddress),1))
GUIctrlSetData($answerupperright,StringTrimRight(_readText2($choiceupperrightAddress),1))
GUIctrlSetData($answerlowerleft,StringTrimRight(_readText2($choicelowerleftAddress),1))
GUIctrlSetData($answerlowerright,StringTrimRight(_readText2($choicelowerrightAddress),1))

Until StringLen(_readText2($questionAddress)) >= 1 And StringLen(_readText2($choiceupperleftAddress)) >= 1 And StringLen(_readText2($choiceupperrightAddress)) >= 1 And StringLen(_readText2($choicelowerleftAddress)) >= 1 And StringLen(_readText2($choicelowerrightAddress)) >= 1


Sleep (Number(GUICtrlRead($delay6))) ;delay2


EndFunc

Func GetPosUpperLeft()
    $pos = MouseGetPos()
    GUIctrlSetData($upperleftX,$pos[0])
    GUIctrlSetData($upperleftY,$pos[1])
EndFunc

Func GetPosLowerLeft()
    $pos = MouseGetPos()
    GUIctrlSetData($lowerleftX,$pos[0])
    GUIctrlSetData($lowerleftY,$pos[1])
EndFunc

Func GetPosUpperRight()
    $pos = MouseGetPos()
    GUIctrlSetData($upperrightX,$pos[0])
    GUIctrlSetData($upperrightY,$pos[1])
EndFunc

Func GetPosLowerRight()
    $pos = MouseGetPos()
    GUIctrlSetData($lowerrightX,$pos[0])
    GUIctrlSetData($lowerrightY,$pos[1])
EndFunc


Func Login ()

If Not (GUICtrlRead($username) = "" Or GUICtrlRead($password) = "") then

$count2 = 0

FileChangeDir (GUICtrlRead($tantradir))
$Process = 'HTLauncher.exe'

GUICtrlSetData($statuslabel,"Auto-login started...")

$i_pid = Run(GUICtrlRead($tantradir)&"Tantra.exe")
;$i_pid = Run(GUICtrlRead($tantradir)&$Process)

ProcessWait($i_pid)

$ProcessUpdate = 'Update.exe' ;-> Target process
ProcessWait($ProcessUpdate)
$PID = ProcessExists($ProcessUpdate) ;-> Get Process ID
Sleep (Number(GUICtrlRead($delay1))) ;delay1

Send("{ENTER}")
Sleep(500)

While WinExists("Update")
  Sleep(500)
  Send("{ENTER}")
  Sleep(500)
  Send("{ENTER}")
Wend

Sleep (Number(GUICtrlRead($delay2))) ;delay2

WinWait("Tantra Launcher")
WinSetTitle("Tantra Launcher", "", GUICtrlRead($wintitle))
$handle = WinGetHandle(GUICtrlRead($wintitle))

$PID = WinGetProcess(GUICtrlRead($wintitle))

ClickInControl("left",GUICtrlRead($wintitle),"","",520,440)

ControlSend($handle, "", "","{BS 15}")
ControlSend($handle, "", "","{DEL 15}")
ControlSend($handle, "", "",GUICtrlRead($username))
ControlSend($handle, "", "","{TAB}")
ControlSend($handle, "", "","{BS 15}")
ControlSend($handle, "", "","{DEL 15}")
ControlSend($handle, "", "",GUICtrlRead($password))
ControlSend($handle, "", "","{ENTER}")
Sleep (Number(GUICtrlRead($delay3))) ; delay3

Select
  Case GUICtrlRead($serverselect) = "Manas"
	$serverY = 260
  Case GUICtrlRead($serverselect) = "Diyana"
	$serverY = 295
  Case GUICtrlRead($serverselect) = "Manas"
	$serverY = 328
  Case GUICtrlRead($serverselect) = "Samadi"
	$serverY = 363
  Case GUICtrlRead($serverselect) = "Warzone"
	$serverY = 395
EndSelect

ClickInControl("left",GUICtrlRead($wintitle),"","",478,$serverY)
Sleep (50)
ClickInControl("left",GUICtrlRead($wintitle),"","",478,$serverY)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",478,$serverY)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",478,$serverY)

ControlSend($handle, "", "","{ENTER}")
Sleep (Number(GUICtrlRead($delay4))) ; delay4

Select
  Case GUICtrlRead($characterselect) = "Character1"
	$charX = 278
	$charY = 484
	$char2X = 172
	$char2Y = 428
  Case GUICtrlRead($characterselect) = "Character2"
	$charX = 514
	$charY = 463
	$char2X = 200
	$char2Y = 429
  Case GUICtrlRead($characterselect) = "Character3"
	$charX = 733
	$charY = 440
	$char2X = 645
	$char2Y = 429
EndSelect

ClickInControl("left",GUICtrlRead($wintitle),"","",$charX,$charY)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$charX,$charY)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$charX,$charY)
Sleep (500)

ControlSend($handle, "", "","{ENTER}")
Sleep (Number(GUICtrlRead($delay5))) ;delay5

ClickInControl("left",GUICtrlRead($wintitle),"","",$char2X,$char2Y)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$char2X,$char2Y)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$char2X,$char2Y)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$char2X,$char2Y)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$char2X,$char2Y)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$char2X,$char2Y)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$char2X,$char2Y)
Sleep (500)
ClickInControl("left",GUICtrlRead($wintitle),"","",$char2X,$char2Y)


Else
   MsgBox(0, "Warning", "Username and/or password field has no value.")

EndIf

EndFunc

Func _OpenFileDialog()

FileChangeDir ($currentdir)

If FileExists("dialog.txt") Then

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))
        ;Msgbox(0,"Info", $currentdir)

	$file = FileOpen("dialog.txt", 0)
	; Check if file opened for reading OK
	If $file = -1 Then
	   MsgBox(0, "Error", "Unable to open file.")
	   Exit
	EndIf

	; Read in lines of text until the EOF is reached
	;MsgBox(0, "Question:", GUICtrlRead($question))
	While 1
	   $line = FileReadLine($file)
	   If @error = -1 Then ExitLoop

	   If StringInStr($line,GUICtrlRead($question)) >= 1 Then
		   $ans = StringRight($line, StringLen($line) - StringInStr($line, ","))
		   Select
		   Case $ans = GUICtrlRead($answerupperleft)
			;MsgBox(0, "location","upper left")
			$myX = Number(GUICtrlRead($upperleftX))
			$myY = Number(GUICtrlRead($upperleftY))
		   Case $ans = GUICtrlRead($answerlowerleft)
			;MsgBox(0, "location","lower left")
			$myX = Number(GUICtrlRead($lowerleftX))
			$myY = Number(GUICtrlRead($lowerleftY))
		   Case $ans = GUICtrlRead($answerupperright)
			;MsgBox(0, "location","upper right")
			$myX = Number(GUICtrlRead($upperrightX))
			$myY = Number(GUICtrlRead($upperrightY))
		   Case $ans = GUICtrlRead($answerlowerright)
			;MsgBox(0, "location","lower right")
			$myX = Number(GUICtrlRead($lowerrightX))
			$myY = Number(GUICtrlRead($lowerrightY))
		   EndSelect

		   ClickInControl("left",GUICtrlRead($wintitle),"","",$myX,$myY)
                   $handle = WinGetHandle(GUICtrlRead($wintitle))
                   ;GUICtrlSetData($wintitle, GUICtrlRead($wintitle))
		   WinSetTitle($handle, "", GUICtrlRead($wintitle))
		   GetCharacterStatus()
	   EndIf
	Wend
	FileClose($file)
Else
	MsgBox(4096,"Warning", "dialog.txt file does not exist. Unable to continue")
EndIf

EndFunc

Func CheckConnection()

FileChangeDir ($currentdir)

RunWait(@ComSpec & " /c " & "netstat.exe -na -p TCP > ip.txt","",@SW_HIDE)
sleep(1000)

If FileExists("tantraip.txt") Then
        ;Msgbox(0,"Info", $currentdir)

	$file = FileOpen("tantraip.txt", 0)
	; Check if file opened for reading OK
	If $file = -1 Then
	   MsgBox(0, "Error", "Unable to open file.")
	   Exit
	EndIf

	While 1
	   $line = FileReadLine($file)
	   If @error = -1 Then ExitLoop
           ;MsgBox(0, "TantraIP", $line)
	   $fileip = FileOpen("ip.txt", 0)
	   If $fileip = -1 Then
	      MsgBox(0, "Error", "Unable to open ip.txt file.")
	      Exit
	   EndIf
	   While 1
		$ipline = FileReadLine($fileip)
		If @error = -1 Then ExitLoop
                If StringInStr($ipline,$line) > 1 then
		  FileClose($fileip)
		  FileClose($file)
		  Return 1
		EndIf
	   Wend
	Wend
        FileClose($fileip)
	FileClose($file)

Else
	MsgBox(4096,"Warning", "dialog.txt file does not exist. Unable to continue")
EndIf

Return 0

EndFunc

Func checkifdisconnected()
	CheckIfPause()
	If GUICtrlRead($checkboxautologin) = 1 Then
	    If CheckConnection() = 0 Or Not WinExists(GUICtrlRead($wintitle)) then
                ;If Not WinExists(GUICtrlRead($wintitle)) then
		;   GUICtrlSetData($wintitle, "Tantra Launcher")
		;EndIf
		$handle = WinGetHandle(GUICtrlRead($wintitle))
		$PID = WinGetProcess(GUICtrlRead($wintitle))
                If $PID then
		   ProcessClose($PID)
		   ProcessWaitClose($PID)
                EndIf
		$internectcheckcount = 1
		Do
		  GUICtrlSetData($statuslabel,"Checking internet connection..."&String($internectcheckcount)&" sec/s")
		  $internectcheckcount = $internectcheckcount + 1
		  Sleep (1000)
		Until CheckInternetConnection() = 1
		GUICtrlSetData($statuslabel,"Checking internet connection..."&" OK")
                Sleep(Number(GUICtrlRead($autologindelay)))
		Call ("Login")
	    	Call ("_getDialogQuestion")
	    	_OpenFileDialog()
		$count2 = 0
 	 	_writeValue(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset), 49)
		$dialogTradeRequest = _GetDialogData(_GetAddressOffset($baseAddressDialogTrade,$TradeOffset))
		$maxHP = _GetData($baseAddressCharacter,$myMaxHPoffset)
		_writeValue(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset), 49)
		$dialogParty = _GetDialogData(_GetAddressOffset($baseAddressDialog,$invitedPartyOffset))
	    EndIf
	EndIf
EndFunc

Func MandaraTeleportHack()


GUISwitch($autologinmenu)
GUISetState (@SW_HIDE)
GUISwitch($botmenu)
GUISetState (@SW_HIDE)
GUISwitch($teleportgui)
GUISetState (@SW_SHOW)

while 1

Do
	$handle = WinGetHandle(GUICtrlRead($wintitle))
	$PID = WinGetProcess(GUICtrlRead($wintitle))
	$msg = GUIGetMsg()
	If $msg = $infoabout3 then MyAbout()
	If $msg = $menumain3 then MenuLaunch()
	If $msg = $saveitem3 then SaveConfig()
	If $msg = $fileitem3 then OpenConfig()
	If $msg = $menuteleport3 then MandaraTeleportHack()
	If $msg = $menuautologin3 then AutoLoginMenu()

	$status1 = GUICtrlRead($APP1_ComboBOX)
If $status1 = "Mandara Village" Then $Value = '101'
If $status1 = "Mandara Portal" Then $Value = '103'
If $status1 = "Entrance to Mara Land" Then $Value = '104'
If $status1 = "Way to Coliseum" Then $Value = '105'
If $status1 = "Entrance to Shambala" Then $Value = '116'
If $status1 = "Anu Village" Then $Value = '117'
If $status1 = "Shambala Castle" Then $Value = '118'
If $status1 = "Holy Army's Barracks" Then $Value = '119'
If $status1 = "Town of Ruins" Then $Value = '120'
If $status1 = "Town of Ruins Westside Forest(1000)" Then $Value = '121'
If $status1 = "Horseman Town Well" Then $Value = '131'
If $status1 = "Horseman Dungeon 1st(600)" Then $Value = '124'
If $status1 = "Horseman Dungeon 1st end" Then $Value = '125'
If $status1 = "Horseman Dungeon 2nd(800)" Then $Value = '126'
If $status1 = "Shambala Dungeon" Then $Value = '123'
If $status1 = "Shambala Dungeon 1st(600)" Then $Value = '128'
If $status1 = "Shambala Dungeon 1st end" Then $Value = '129'
If $status1 = "Shambala Dungeon 2st(1000)" Then $Value = '130'
If $status1 = "Shambala Castle" Then $Value = '132'
If $status1 = "Jina Village Portal" Then $Value = '133'
If $status1 = "Rest Place(400)" Then $Value = '134'
If $status1 = "Country Borderline(400)" Then $Value = '135'
If $status1 = "Small Sea Port" Then $Value = '136'
If $status1 = "Pamir Plains(400)" Then $Value = '138'
If $status1 = "Mantra Temple" Then $Value = '140'
If $status1 = "Village of the Outcast" Then $Value = '141'
If $status1 = "Flower Field(400)" Then $Value = '142'
If $status1 = "Temple of Fire(400)" Then $Value = '143'
If $status1 = "Brahma Castle" Then $Value = '161'
If $status1 = "Vishnu Castle" Then $Value = '162'
If $status1 = "Shiva Castle" Then $Value = '163'
If $status1 = "Brahman Front Line" Then $Value = '164'
If $status1 = "Brahman Right Flank" Then $Value = '165'
If $status1 = "Brahman Left Flank" Then $Value = '166'
If $status1 = "Vishnite Front Line" Then $Value = '167'
If $status1 = "Vishnite Right Flank" Then $Value = '168'
If $status1 = "Vishnite Left Flank" Then $Value = '169'
If $status1 = "Shivan Front Line" Then $Value = '170'
If $status1 = "Shivan Right Flank" Then $Value = '171'
If $status1 = "Shivan Left Flank" Then $Value = '172'
If $status1 = "Brahman Chaturanga" Then $Value = '173'
If $status1 = "SHivan Chaturanga" Then $Value = '174'
If $status1 = "Vishnite Chaturanga" Then $Value = '175'
If $status1 = "Emperor's Tomb Entrance" Then $Value = '190'
If $status1 = "[Not Yet Implemented]" Then $Value = '191'
If $status1 = "West Tomb(20000)" Then $Value = '192'
If $status1 = "East Tomb(20000)" Then $Value = '193'
If $status1 = "Mahi Durga" Then $Value = '194'
If $status1 = "Ahb Durga" Then $Value = '195'
If $status1 = "Danva Durga" Then $Value = '196'
If $status1 = "Nar Durga" Then $Value = '197'
If $status1 = "Mahi Durga Gate" Then $Value = '198'
If $status1 = "Ahb Durga Gate" Then $Value = '199'
If $status1 = "Danva Durga Gate" Then $Value = '200'
If $status1 = "Nar Durga Gate" Then $Value = '201'
If $status1 = "Mahi Durga Stronghold" Then $Value = '202'
If $status1 = "Ahb Durga Stronghold" Then $Value = '203'
If $status1 = "Danva Durga Stronghold" Then $Value = '204'
If $status1 = "Nar Durga Stronghold" Then $Value = '205'
If $status1 = "Mahi Conference Hall" Then $Value = '206'
If $status1 = "Ahb Conference Hall" Then $Value = '207'
If $status1 = "Danva Conference Hall" Then $Value = '208'
If $status1 = "Nar Conference Hall" Then $Value = '209'
If $status1 = "Mahi Kaustbam" Then $Value = '216'
If $status1 = "Ahb Kaustbam" Then $Value = '217'
If $status1 = "Danva Kaustbam" Then $Value = '218'
If $status1 = "Nar Kaustbam" Then $Value = '219'
If $status1 = "Town Biryu" Then $Value = '220'
If $status1 = "Biryu Commercial Area" Then $Value = '221'
If $status1 = "Biryu Downtown Area" Then $Value = '222'
If $status1 = "[Not Yet Implemented]" Then $Value = '232'
If $status1 = "Mudha Dungeon[x]" Then $Value = '233'
	;$status2 = GUICtrlRead($APP1_ComboBOX2)
	IF $msg = $RUN then _Teleport(Number($Value)) ;ReadAllPortals()
	IF $msg = $RUN2 then
	    $v_Read = _searchMultiByte(Number(GUICtrlRead($searchStart)), GUICtrlRead($searchBytes), $searchlen, Number(GUICtrlRead($bytes2read)))
            If $v_Read <> 0 then
	        Msgbox(0,"Info","Target address found: "&Hex($v_Read))
	        GUICtrlSetData($inputAddress,$v_Read)
	    Else
                Msgbox(0,"Info","Target address NOT found." & @CRLF & @CRLF & "Possible causes:" & @CRLF & "1. You have used this program already to hack the target address." & @CRLF & "2. You have launched Tantra with modified parameters.")
	    EndIf
	EndIf

	IF $msg = $BACK2 Or $msg = $GUI_EVENT_CLOSE then
		GUISwitch($teleportgui)
		GUISetState (@SW_HIDE)
                GUISwitch($botmenu)
		GUISetState (@SW_SHOW)
		Send("+!m")
	EndIf


Until $msg = $EXIT

Wend

EndFunc


Func _Teleport($Value) ;
   If $Value <> 0 then
	$handle = WinGetHandle(GUICtrlRead($wintitle))
	$PID = WinGetProcess(GUICtrlRead($wintitle))
	$OpenProcess = _MemOpen(0x38, False, $PID) ;-> Enable reading/writing to the process and get the handle
        $v_Read = _MemWrite($OpenProcess, Number(GUICtrlRead($inputAddress)), $Value, 1);-> Write a new 1 byte value to the defined address
	_MemClose($OpenProcess) ;-> Disable reading/writing to the process
	MsgBox(0,"Info","Click Mandara portal now.")
   Else
	MsgBox(0,"Info","Target address is invalid.")
   EndIf
EndFunc


Func _searchMultiByte($Address, $searchBytes, $searchlen, $bytes2read)

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))

Do
    GUICtrlSetData($currentaddress, $Address)
    $a1 = _GetBytesArray($Address, $bytes2read)
    ;$a1 = _MemRead($OpenProcess, $Address, $bytes2read) ;-> Read a 1 byte value from the defined address
    $result = StringInStr(Hex($a1), $searchBytes)
    $result = ($result - 1)/2
    $myAddress = $Address + $result

    $Address = $Address + $bytes2read - $searchlen
    ;$loop = $loop + 1

    If $result >= 0 then
	Return $myAddress
    EndIf

Until $Address >= Number(GUICtrlRead($searchEnd)) Or $result >= 0

    Return 0


EndFunc


Func _GetBytesArray($Address,$bytes)

    $handle = WinGetHandle(GUICtrlRead($wintitle))
    $PID = WinGetProcess(GUICtrlRead($wintitle))
    $OpenProcess = _MemOpen(0x38, False, $PID) ;-> Enable reading/writing to the process and get the handle
    $byte1 = _MemRead($OpenProcess, $Address, $bytes) ;-> Read a 1 byte value from the defined address
    _MemClose($OpenProcess) ;-> Disable reading/writing to the process
    Return $byte1
EndFunc

Func SaveConfig()

$configfile = FileSaveDialog( "Choose a name.", $currentdir, "booster101 (*.cfg)", 2)

If StringInStr($configfile,".cfg") >= 1 then
	$file = FileOpen($configfile, 2)
Else
	$file = FileOpen($configfile&".cfg", 2)
EndIf
If $file = -1 Then
  MsgBox(0, "Error", "Unable to open file.")
  Exit
EndIf

;Msgbox(0,"info",$checkLoot)

FileWriteLine($file, $checkLoot&","&GUICtrlRead($checkLoot)&","&GUICtrlGetState($checkLoot)&",checkbox")
FileWriteLine($file, $lootduration&","&GUICtrlRead($lootduration)&","&GUICtrlGetState($lootduration)&",")
$text = StringReplace(GUICtrlRead($MonsterCombo), @CRLF, "-")
FileWriteLine($file, $MonsterCombo&","&$text&","&GUICtrlGetState($MonsterCombo)&",edit")
FileWriteLine($file, $checkMonsterSelectTarget&","&GUICtrlRead($checkMonsterSelectTarget)&","&GUICtrlGetState($checkMonsterSelectTarget)&",checkbox")
FileWriteLine($file, $ClearMonsterList&","&GUICtrlRead($ClearMonsterList)&","&GUICtrlGetState($ClearMonsterList)&",")
FileWriteLine($file, $AddMonsterList&","&GUICtrlRead($AddMonsterList)&","&GUICtrlGetState($AddMonsterList)&",")

FileWriteLine($file, $checkRepair&","&GUICtrlRead($checkRepair)&","&GUICtrlGetState($checkRepair)&",checkbox")
FileWriteLine($file, $checkSkill&","&GUICtrlRead($checkSkill)&","&GUICtrlGetState($checkSkill)&",checkbox")
FileWriteLine($file, $checkSkillR&","&GUICtrlRead($checkSkillR)&","&GUICtrlGetState($checkSkillR)&",checkbox")
FileWriteLine($file, $F1Skill1&","&GUICtrlRead($F1Skill1)&","&GUICtrlGetState($F1Skill1)&",checkbox")
FileWriteLine($file, $F1Skill1Delay&","&GUICtrlRead($F1Skill1Delay)&","&GUICtrlGetState($F1Skill1Delay)&",")
FileWriteLine($file, $F1Skill2&","&GUICtrlRead($F1Skill2)&","&GUICtrlGetState($F1Skill2)&",checkbox")
FileWriteLine($file, $F1Skill2Delay&","&GUICtrlRead($F1Skill2Delay)&","&GUICtrlGetState($F1Skill2Delay)&",")
FileWriteLine($file, $F1Skill3&","&GUICtrlRead($F1Skill3)&","&GUICtrlGetState($F1Skill3)&",checkbox")
FileWriteLine($file, $F1Skill3Delay&","&GUICtrlRead($F1Skill3Delay)&","&GUICtrlGetState($F1Skill3Delay)&",")
FileWriteLine($file, $F1Skill4&","&GUICtrlRead($F1Skill4)&","&GUICtrlGetState($F1Skill4)&",checkbox")
FileWriteLine($file, $F1Skill4Delay&","&GUICtrlRead($F1Skill4Delay)&","&GUICtrlGetState($F1Skill4Delay)&",")
FileWriteLine($file, $F1Skill7&","&GUICtrlRead($F1Skill7)&","&GUICtrlGetState($F1Skill7)&",checkbox")
FileWriteLine($file, $F1Skill7Delay&","&GUICtrlRead($F1Skill7Delay)&","&GUICtrlGetState($F1Skill7Delay)&",")
FileWriteLine($file, $F1Skill7percent&","&GUICtrlRead($F1Skill7percent)&","&GUICtrlGetState($F1Skill7percent)&",")
FileWriteLine($file, $F1Skill8&","&GUICtrlRead($F1Skill8)&","&GUICtrlGetState($F1Skill8)&",checkbox")
FileWriteLine($file, $F1Skill8Delay&","&GUICtrlRead($F1Skill8Delay)&","&GUICtrlGetState($F1Skill8Delay)&",")
FileWriteLine($file, $F1Skill8percent&","&GUICtrlRead($F1Skill8percent)&","&GUICtrlGetState($F1Skill8percent)&",")
FileWriteLine($file, $F1Skill9&","&GUICtrlRead($F1Skill9)&","&GUICtrlGetState($F1Skill9)&",checkbox")
FileWriteLine($file, $F1Skill9Delay&","&GUICtrlRead($F1Skill9Delay)&","&GUICtrlGetState($F1Skill9Delay)&",")
FileWriteLine($file, $F1Skill9percent&","&GUICtrlRead($F1Skill9percent)&","&GUICtrlGetState($F1Skill9percent)&",")
FileWriteLine($file, $DelayAfterFindTargetCheckBox&","&GUICtrlRead($DelayAfterFindTargetCheckBox)&","&GUICtrlGetState($DelayAfterFindTargetCheckBox)&",checkbox")
FileWriteLine($file, $DelayAfterFindTargetDelay&","&GUICtrlRead($DelayAfterFindTargetDelay)&","&GUICtrlGetState($DelayAfterFindTargetDelay)&",")
FileWriteLine($file, $checkReplyPM&","&GUICtrlRead($checkReplyPM)&","&GUICtrlGetState($checkReplyPM)&",checkbox")
FileWriteLine($file, $checkReplyText&","&GUICtrlRead($checkReplyText)&","&GUICtrlGetState($checkReplyText)&",")
FileWriteLine($file, $partyOK&","&GUICtrlRead($partyOK)&","&GUICtrlGetState($partyOK)&",checkbox")
FileWriteLine($file, $partyjoinedreply&","&GUICtrlRead($partyjoinedreply)&","&GUICtrlGetState($partyjoinedreply)&",")
FileWriteLine($file, $partydeniedreply&","&GUICtrlRead($partydeniedreply)&","&GUICtrlGetState($partydeniedreply)&",")
FileWriteLine($file, $canceltraderequest&","&GUICtrlRead($canceltraderequest)&","&GUICtrlGetState($canceltraderequest)&",checkbox")
FileWriteLine($file, $canceltraderequestreply&","&GUICtrlRead($canceltraderequestreply)&","&GUICtrlGetState($canceltraderequestreply)&",")
FileWriteLine($file, $checkboxautologin&","&GUICtrlRead($checkboxautologin)&","&GUICtrlGetState($checkboxautologin)&",checkbox")
FileWriteLine($file, $antistuckup&","&GUICtrlRead($antistuckup)&","&GUICtrlGetState($antistuckup)&",checkbox")
FileWriteLine($file, $antistuckspin&","&GUICtrlRead($antistuckspin)&","&GUICtrlGetState($antistuckspin)&",")
FileWriteLine($file, $antistuckmove&","&GUICtrlRead($antistuckmove)&","&GUICtrlGetState($antistuckmove)&",")
FileWriteLine($file, $autosilf&","&GUICtrlRead($autosilf)&","&GUICtrlGetState($autosilf)&",checkbox")
FileWriteLine($file, $autosilfxcoord&","&GUICtrlRead($autosilfxcoord)&","&GUICtrlGetState($autosilfxcoord)&",")
FileWriteLine($file, $autosilfycoord&","&GUICtrlRead($autosilfycoord)&","&GUICtrlGetState($autosilfycoord)&",")
FileWriteLine($file, $F2Skill1&","&GUICtrlRead($F2Skill1)&","&GUICtrlGetState($F2Skill1)&",checkbox")
FileWriteLine($file, $F2Skill1Delay&","&GUICtrlRead($F2Skill1Delay)&","&GUICtrlGetState($F2Skill1Delay)&",")
FileWriteLine($file, $F2Skill2&","&GUICtrlRead($F2Skill2)&","&GUICtrlGetState($F2Skill2)&",checkbox")
FileWriteLine($file, $F2Skill2Delay&","&GUICtrlRead($F2Skill2Delay)&","&GUICtrlGetState($F2Skill2Delay)&",")
FileWriteLine($file, $F2Skill3&","&GUICtrlRead($F2Skill3)&","&GUICtrlGetState($F2Skill3)&",checkbox")
FileWriteLine($file, $F2Skill3Delay&","&GUICtrlRead($F2Skill3Delay)&","&GUICtrlGetState($F2Skill3Delay)&",")
FileWriteLine($file, $F2Skill4&","&GUICtrlRead($F2Skill4)&","&GUICtrlGetState($F2Skill4)&",checkbox")
FileWriteLine($file, $F2Skill4Delay&","&GUICtrlRead($F2Skill4Delay)&","&GUICtrlGetState($F2Skill4Delay)&",")
FileWriteLine($file, $F2Skill6&","&GUICtrlRead($F2Skill6)&","&GUICtrlGetState($F2Skill6)&",checkbox")
FileWriteLine($file, $F2Skill6Delay&","&GUICtrlRead($F2Skill6Delay)&","&GUICtrlGetState($F2Skill6Delay)&",")
FileWriteLine($file, $F2Skill7&","&GUICtrlRead($F2Skill7)&","&GUICtrlGetState($F2Skill7)&",checkbox")
FileWriteLine($file, $F2Skill7Delay&","&GUICtrlRead($F2Skill7Delay)&","&GUICtrlGetState($F2Skill7Delay)&",")
FileWriteLine($file, $F2Skill9&","&GUICtrlRead($F2Skill9)&","&GUICtrlGetState($F2Skill9)&",checkbox")
FileWriteLine($file, $F2Skill9Delay&","&GUICtrlRead($F2Skill9Delay)&","&GUICtrlGetState($F2Skill9Delay)&",")
FileWriteLine($file, $characterselect&","&GUICtrlRead($characterselect)&","&GUICtrlGetState($characterselect)&",")
FileWriteLine($file, $serverselect&","&GUICtrlRead($serverselect)&","&GUICtrlGetState($serverselect)&",")
FileWriteLine($file, $delay1&","&GUICtrlRead($delay1)&","&GUICtrlGetState($delay1)&",")
FileWriteLine($file, $delay2&","&GUICtrlRead($delay2)&","&GUICtrlGetState($delay2)&",")
FileWriteLine($file, $delay3&","&GUICtrlRead($delay3)&","&GUICtrlGetState($delay3)&",")
FileWriteLine($file, $delay4&","&GUICtrlRead($delay4)&","&GUICtrlGetState($delay4)&",")
FileWriteLine($file, $delay5&","&GUICtrlRead($delay5)&","&GUICtrlGetState($delay5)&",")
FileWriteLine($file, $delay6&","&GUICtrlRead($delay6)&","&GUICtrlGetState($delay6)&",")
FileWriteLine($file, $upperleftX&","&GUICtrlRead($upperleftX)&","&GUICtrlGetState($upperleftX)&",")
FileWriteLine($file, $upperleftY&","&GUICtrlRead($upperleftY)&","&GUICtrlGetState($upperleftY)&",")
FileWriteLine($file, $upperrightX&","&GUICtrlRead($upperrightX)&","&GUICtrlGetState($upperrightX)&",")
FileWriteLine($file, $upperrightY&","&GUICtrlRead($upperrightY)&","&GUICtrlGetState($upperrightY)&",")
FileWriteLine($file, $lowerleftX&","&GUICtrlRead($lowerleftX)&","&GUICtrlGetState($lowerleftX)&",")
FileWriteLine($file, $lowerleftY&","&GUICtrlRead($lowerleftY)&","&GUICtrlGetState($lowerleftY)&",")
FileWriteLine($file, $lowerrightX&","&GUICtrlRead($lowerrightX)&","&GUICtrlGetState($lowerrightX)&",")
FileWriteLine($file, $lowerrightY&","&GUICtrlRead($lowerrightY)&","&GUICtrlGetState($lowerrightY)&",")
FileWriteLine($file, $tantradir&","&GUICtrlRead($tantradir)&","&GUICtrlGetState($tantradir)&",")

FileWriteLine($file, $bytes2read&","&GUICtrlRead($bytes2read)&","&GUICtrlGetState($bytes2read)&",")
FileWriteLine($file, $searchStart&","&GUICtrlRead($searchStart)&","&GUICtrlGetState($searchStart)&",")
FileWriteLine($file, $searchBytes&","&GUICtrlRead($searchBytes)&","&GUICtrlGetState($searchBytes)&",")
FileWriteLine($file, $currentaddress&","&GUICtrlRead($currentaddress)&","&GUICtrlGetState($currentaddress)&",")
FileWriteLine($file, $inputAddress&","&GUICtrlRead($inputAddress)&","&GUICtrlGetState($inputAddress)&",")
FileWriteLine($file, $APP1_ComboBOX&","&GUICtrlRead($APP1_ComboBOX)&","&GUICtrlGetState($APP1_ComboBOX)&",combobox")
FileWriteLine($file, $autologindelay&","&GUICtrlRead($autologindelay)&","&GUICtrlGetState($autologindelay)&",")



FileClose($file)


EndFunc


Func OpenConfig()

$configfile = FileOpenDialog( "Choose a name.", $currentdir, "booster101 (*.cfg)", 2)
;If @error <> 1 Then GUICtrlCreateMenuitem ($configfile,$recentfilesmenu)

$file = FileOpen($configfile, 0)

If $file = -1 Then
  MsgBox(0, "Error", "Unable to open file.")
  Exit
EndIf

While 1
   $line = FileReadLine($file)
   If @error = -1 Then ExitLoop
   $linelen = StringLen($line)
   $linecomma = StringInStr($line,",")
   $linecomma2 = StringInStr($line,",", 0, 2)
   $linecomma3 = StringInStr($line,",", 0, 3)
   $attribute = StringLeft($line, $linecomma - 1)

   $mytmp = StringLeft($line, $linecomma2 - 1)
   $mytmplen = StringLen($mytmp)
   $mytmpcomma = StringInStr($mytmp,",")
   $attributevalue = StringRight($mytmp, $mytmplen - $mytmpcomma)

   $mytmp = StringLeft($line, $linecomma3 - 1)
   $mytmplen = StringLen($mytmp)
   $attributestate = StringRight($mytmp, $mytmplen - $linecomma2)
   ;MsgBox(0,"info","attrib = "&$attribute & @CRLF & "value = "&$attributevalue & @CRLF & "State = "&$attributestate)

   If StringInStr($line, "checkbox") then
     GUICtrlSetState($attribute, $attributevalue)
     GUICtrlSetState($attribute, $attributestate)
   ElseIf StringInStr($line, "combobox") then

	GUICtrlSetData($attribute,"Mandara Portal|Entrance to Mara Land|Way to Coliseum|Entrance to Shambala|Anu Village|Shambala Castle|Holy Army's Barracks|Town of Ruins|Town of Ruins Westside Forest(1000)|Horseman Town Well|Horseman Dungeon 1st(600)|Horseman Dungeon 1st end|Horseman Dungeon 2nd(800)|Shambala Dungeon|Shambala Dungeon 1st(600)|Shambala Dungeon 1st end|Shambala Dungeon 2st(1000)|Shambala Castle|Jina Village Portal|Rest Place(400)|Country Borderline(400)|Small Sea Port|Pamir Plains(400)|Mantra Temple|Village of the Outcast|Flower Field(400)|Temple of Fire(400)|Brahma Castle|Vishnu Castle|Shiva Castle|Brahman Front Line|Brahman Right Flank|Brahman Left Flank|Vishnite Front Line|Vishnite Right Flank|Vishnite Left Flank|Shivan Front Line|Shivan Right Flank|Shivan Left Flank|Brahman Chaturanga|SHivan Chaturanga|Vishnite Chaturanga|Emperor's Tomb Entrance|[Not Yet Implemented]|West Tomb(20000)|East Tomb(20000)|Mahi Durga|Ahb Durga|Danva Durga|Nar Durga|Mahi Durga Gate|Ahb Durga Gate|Danva Durga Gate|Nar Durga Gate|Mahi Durga Stronghold|Ahb Durga Stronghold|Danva Durga Stronghold|Nar Durga Stronghold|Mahi Conference Hall|Ahb Conference Hall|Danva Conference Hall|Nar Conference Hall|Mahi Kaustbam|Ahb Kaustbam|Danva Kaustbam|Nar Kaustbam|Town Biryu|Biryu Commercial Area|Biryu Downtown Area|[Not Yet Implemented]|Mudha Dungeon[x]",$attributevalue)
      GUICtrlSetState($attribute, $attributestate)
   ElseIf StringInStr($line, "edit") then
      $attributevalue = StringReplace($attributevalue, "-", @CRLF)
      GUICtrlSetData($attribute, $attributevalue)
      GUICtrlSetState($attribute, $attributestate)
   Else
      GUICtrlSetData($attribute, $attributevalue)
      GUICtrlSetState($attribute, $attributestate)
   EndIf
Wend

FileClose($file)

EndFunc

Func OpenReadMeFile()


If FileExists("readme.txt") Then
        $rdme = ""
        ;Msgbox(0,"Info", $currentdir)

	$file = FileOpen("readme.txt", 0)
	; Check if file opened for reading OK
	If $file = -1 Then
	   MsgBox(0, "Error", "Unable to open file.")
	   Exit
	EndIf

While 1
	$chars = FileRead($file, 1)
	If @error = -1 Then ExitLoop
        $rdme = $rdme + $chars

Wend

	FileClose($file)

   MsgBox(0, "Char read:", $rdme)
EndIf

EndFunc

Func MyAbout()

MsgBox(0,"About","Tantra Bot System Version "&$versionnumber& @CRLF & @CRLF & "Visit http://www.luciotan.com/board/thread-9644-1-1.html for updates." & @CRLF & @CRLF & "For web hosting and domain registration needs, please visit http://www.bigbytes.net")


EndFunc


Func ClickInControl( $szLR, $szTitle, $szText, $szControl, $nX, $nY )
  If $szLR <> "left" And $szLR <> "right" Then Return 0
  $hWnd = ControlGetHandle( $szTitle, $szText, $szControl )
  If @error Then Return 0
  $hWndDad = WinGetHandle( $szTitle, $szText )
  If @error Then Return 0
  $me = DLLCall( "kernel32.dll", "long", "GetCurrentThreadId" )
  $you = DLLCall( "user32.dll", "long", "GetWindowThreadProcessId", "hwnd", $hWndDad, "long_ptr", 0)
  $coord = _MakeLong( $nX, $nY )
  If $szLR = "left" Then
     $msg1 = 0x0201;WM_LBUTTONDOWN
     $msg2 = 0x0202;WM_LBUTTONUP
     $i = 0x0001;MK_LBUTTON
  Else
     $msg1 = 0x0204;WM_RBUTTONDOWN
     $msg2 = 0x0205;WM_RBUTTONUP
     $i = 0x0002;MK_RBUTTON
  EndIf
  $ret = DLLCall("user32.dll","int","AttachThreadInput","long",$me[0],"long",$you[0],"int",1)
  If $ret[0] = 0 Then Return 0
  $ret = DLLCall("user32.dll","long","PostMessage","hwnd",$hWnd,"int",$msg1,"int",$i,"int",$coord)
  If $ret[0] = 0 Then Return 0
  Sleep(100)
  $ret = DLLCall("user32.dll","long","PostMessage","hwnd",$hWnd,"int",$msg2,"int",0,"int",$coord)
  If $ret[0] = 0 Then Return 0
  DLLCall("user32.dll","int","AttachThreadInput","long",$me[0],"long",$you[0],"int",0)
  If $ret[0] = 0 Then Return 0
  Return 1
EndFunc

Func _MakeLong($LoWord,$HiWord)
  Return BitOR($HiWord * 0x10000, BitAND($LoWord, 0xFFFF))
EndFunc


Func _ProcessGetFirstWinHandle($process)
  ; Check given process actually exists
   $pid = ProcessExists($process)
   If Not $pid Then
      MsgBox(0,"Info","Given process does not exist: " & $process)
      Return 1
   EndIf

  ; Get window list
   $winList = WinList()
  $text = ""
  ; Find which windows go with this process
   For $i = 0 To $winList[0][0] - 1
      $testPid = WinGetProcess($winList[$i][0])
      ;MsgBox(0, "Details", "Title=" & $winList[$i][0] & @CRLF & "Handle=" & $winList[$i][1] & @CRLF & "$pid = " & $pid & @CRLF & "$testPid = " & $testPid)


      If $testPid = $pid Then
	 MsgBox(0, "Details", "Title=" & $winList[$i][0] & @CRLF & "Handle=" & $winList[$i][1] & @CRLF & "PID = " & $pid)
         ;$a = $winList[$i][1]
	 Return $winList[$i][1]
      EndIf
      ;$text = $text & $winList[$i][0] & " -- " & $winList[$i][1] & " -- " & $testPid & @CRLF
   Next
   Return 0
EndFunc

Func GetCharacterStatus()

If BitAnd(GUICtrlRead($viewstatusitem),$GUI_CHECKED) = $GUI_CHECKED Then
  GUICtrlSetData($statuslabel,GetCharacterStatusData())
EndIf

EndFunc

Func GetCharacterStatusData()

$handle = WinGetHandle(GUICtrlRead($wintitle))
$PID = WinGetProcess(GUICtrlRead($wintitle))

$charname = _readText2($charactername)
$curHP = _GetData($baseAddressCharacter,$myCurHPoffset)
$TPCur = _GetData($baseAddressTargetMonster,$TPCurAddrOffset)

$a = "HP: " & String($curHP) & "  TP: " & $TPCur & "  "& $charname

return $a

EndFunc


Func CheckIfPause()
	If GUICtrlRead($pausecheckbox) = 1 then
		MsgBox(0,"Info","Paused...")
		GUICtrlSetState($pausecheckbox, 4)
		Call ("MenuLaunch")
	EndIf
EndFunc

Func ReadAllPortals()

$portalcount = 1
$startaddress = Number(GUICtrlRead($inputAddress)) - 168
GUICtrlSetData ($MonsterCombo, "",1)

Do

$portal = _readText2($startaddress - 68)
$portalcode = _GetAscii($startaddress)
If $portalcode <> 0 then
	;GUICtrlSetData ($MonsterCombo, @CRLF&String($portalcount)&","&String($portalcode)&","&Hex($portalcode,2)&","&$portal,1)
        _writeValue($startaddress, Number(GUICtrlRead($inputAddress)))
EndIf
$startaddress = $startaddress + 168

$portalcount = $portalcount + 1

Until $portalcount = 96

MsgBox(0,"Info","Click Mandara portal now.")

EndFunc

Func RunDcx()

FileChangeDir ($currentdir)

RunWait("dcxBotv02.exe")


EndFunc


Func CheckInternetConnection()

FileChangeDir ($currentdir)
RunWait(@ComSpec & " /c " & "ping.exe yahoo.com > ip.txt","",@SW_HIDE)
sleep(1000)

If FileExists("tantraip.txt") Then
        ;Msgbox(0,"Info", $currentdir)

	$file = FileOpen("ip.txt", 0)
	; Check if file opened for reading OK
	If $file = -1 Then
	   MsgBox(0, "Error", "Unable to open file.")
	   Exit
	EndIf

	While 1
	   $line = FileReadLine($file)
	   If @error = -1 Then ExitLoop
	   If StringInStr($line,"bytes=") > 1 then
		  FileClose($file)
		  Return 1
	   EndIf
	Wend
	FileClose($file)

Else
	MsgBox(4096,"Warning", "dialog.txt file does not exist. Unable to continue")
EndIf

Return 0


EndFunc

; ----------------------------------------------------------------------------
; <AUT2EXE INCLUDE-END: C:\Documents and Settings\All Users\Documents\Tantra\agit.au3>
; ----------------------------------------------------------------------------
